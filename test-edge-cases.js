// Test script to test edge cases and error conditions
const io = require('socket.io-client');

async function testEdgeCases() {
  console.log('🎮 Testing Scout Edge Cases and Error Conditions');
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null,
      hand: [],
      isMyTurn: false,
      activeSet: null
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Set up game
    console.log('📝 Setting up 3-player game...');
    
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    
    for (let i = 1; i < 3; i++) {
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
    }
    
    const startResult = await new Promise((resolve) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, (success) => {
        resolve(success);
      });
    });
    
    if (!startResult) {
      throw new Error('Failed to start round');
    }
    
    console.log('✅ Game setup complete!');
    
    // Set up event listeners
    players.forEach((player, index) => {
      player.socket.on('roundState', (roundState) => {
        player.hand = roundState.you?.hand || [];
        player.isMyTurn = roundState.activePlayer === player.playerId;
        player.activeSet = roundState.activeSet;
      });
    });
    
    // Wait for initial state
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 1: Try to act when it's not your turn
    console.log('\n🧪 TEST 1: Acting Out of Turn');
    const notMyTurnPlayer = players.find(p => !p.isMyTurn);
    if (notMyTurnPlayer) {
      console.log(`Player ${notMyTurnPlayer.id} trying to show when it's not their turn...`);
      const result = await new Promise((resolve) => {
        notMyTurnPlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 0 
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    // Test 2: Try to show invalid card range
    console.log('\n🧪 TEST 2: Invalid Card Range');
    const currentPlayer = players.find(p => p.isMyTurn);
    if (currentPlayer) {
      // Choose initial orientation first
      await new Promise((resolve) => {
        currentPlayer.socket.emit('chooseInitialHandOrientation', { orientation: 'a' }, resolve);
      });
      
      console.log(`Player ${currentPlayer.id} trying to show invalid range (start > end)...`);
      const result = await new Promise((resolve) => {
        currentPlayer.socket.emit('show', { 
          start: 5, 
          endInclusive: 2  // Invalid: start > end
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    // Test 3: Try to show out-of-bounds cards
    console.log('\n🧪 TEST 3: Out of Bounds Cards');
    if (currentPlayer) {
      console.log(`Player ${currentPlayer.id} trying to show out-of-bounds cards...`);
      const result = await new Promise((resolve) => {
        currentPlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 20  // Invalid: beyond hand size
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    // Test 4: Try to scout when there's no active set
    console.log('\n🧪 TEST 4: Scout with No Active Set');
    if (currentPlayer) {
      console.log(`Player ${currentPlayer.id} trying to scout when there's no active set...`);
      const result = await new Promise((resolve) => {
        currentPlayer.socket.emit('scout', { 
          edge: 'left',
          insertAt: 0,
          orientation: 'a'
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    // Test 5: Valid show to create active set for further tests
    console.log('\n🧪 TEST 5: Create Active Set for Further Tests');
    if (currentPlayer) {
      console.log(`Player ${currentPlayer.id} showing a valid card...`);
      const result = await new Promise((resolve) => {
        currentPlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 0 
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '✅ Success' : `❌ Failed: ${result.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 6: Try to scout with invalid insert position
    console.log('\n🧪 TEST 6: Invalid Scout Insert Position');
    const nextPlayer = players.find(p => p.isMyTurn);
    if (nextPlayer && nextPlayer.activeSet && nextPlayer.activeSet.cards.length > 0) {
      console.log(`Player ${nextPlayer.id} trying to scout with invalid insert position...`);
      const result = await new Promise((resolve) => {
        nextPlayer.socket.emit('scout', { 
          edge: 'left',
          insertAt: -1,  // Invalid: negative position
          orientation: 'a'
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    // Test 7: Try to scout with insert position beyond hand size
    console.log('\n🧪 TEST 7: Scout Insert Position Beyond Hand');
    if (nextPlayer && nextPlayer.activeSet && nextPlayer.activeSet.cards.length > 0) {
      console.log(`Player ${nextPlayer.id} trying to scout with insert position beyond hand size...`);
      const result = await new Promise((resolve) => {
        nextPlayer.socket.emit('scout', { 
          edge: 'left',
          insertAt: 100,  // Invalid: way beyond hand size
          orientation: 'a'
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Result: ${result.ok ? '❌ Unexpectedly succeeded' : `✅ Correctly failed: ${result.error}`}`);
    }
    
    console.log('\n🎮 Edge case testing completed!');
    console.log('💡 Open http://localhost:5173 in your browser to continue playing manually');
    
  } catch (error) {
    console.error('❌ Edge case test failed:', error);
  }
  
  // Keep connections alive
  console.log('\n🔄 Keeping connections alive for manual testing...');
}

testEdgeCases().catch(console.error);
