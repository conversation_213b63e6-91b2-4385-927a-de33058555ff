// Test script to simulate actual Scout gameplay
const io = require('socket.io-client');

async function simulateGameplay() {
  console.log('🎮 Starting Scout Gameplay Test - 3 Players');
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null,
      hand: [],
      isMyTurn: false
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Set up game
    console.log('📝 Setting up 3-player game...');
    
    // Player 1 creates room
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    
    // Players 2 and 3 join
    for (let i = 1; i < 3; i++) {
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
    }
    
    // Start round
    const startResult = await new Promise((resolve) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, (success) => {
        resolve(success);
      });
    });
    
    if (!startResult) {
      throw new Error('Failed to start round');
    }
    
    console.log('✅ Game setup complete!');
    
    // Set up event listeners
    players.forEach((player, index) => {
      player.socket.on('roundState', (roundState) => {
        player.hand = roundState.you?.hand || [];
        player.isMyTurn = roundState.activePlayer === player.playerId;
        
        console.log(`🎯 Player ${index + 1} state:`, {
          handSize: player.hand.length,
          isMyTurn: player.isMyTurn,
          activePlayer: roundState.activePlayer,
          phase: roundState.phase
        });
        
        if (player.hand.length > 0) {
          console.log(`   Hand: ${player.hand.map(c => {
            const [higher, lower] = c.cardId.split('-').map(Number);
            return `${higher}|${lower}`;
          }).join(', ')}`);
        }
      });
    });
    
    // Wait for initial state
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 1: Player 1 chooses initial orientation
    console.log('\n🧪 TEST 1: Initial Hand Orientation');
    const currentPlayer = players.find(p => p.isMyTurn);
    if (currentPlayer) {
      console.log(`Player ${currentPlayer.id} choosing initial orientation...`);
      const orientResult = await new Promise((resolve) => {
        currentPlayer.socket.emit('chooseInitialHandOrientation', { orientation: 'a' }, (ok) => {
          resolve(ok);
        });
      });
      console.log(`Orientation choice: ${orientResult ? '✅ Success' : '❌ Failed'}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 2: Try to show cards
    console.log('\n🧪 TEST 2: Show Cards');
    const activePlayer = players.find(p => p.isMyTurn);
    if (activePlayer && activePlayer.hand.length > 0) {
      console.log(`Player ${activePlayer.id} attempting to show first card...`);
      const showResult = await new Promise((resolve) => {
        activePlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 0 
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Show result: ${showResult.ok ? '✅ Success' : `❌ Failed: ${showResult.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 3: Try to pass
    console.log('\n🧪 TEST 3: Pass Turn');
    const passingPlayer = players.find(p => p.isMyTurn);
    if (passingPlayer) {
      console.log(`Player ${passingPlayer.id} attempting to pass...`);
      const passResult = await new Promise((resolve) => {
        passingPlayer.socket.emit('pass', {}, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Pass result: ${passResult.ok ? '✅ Success' : `❌ Failed: ${passResult.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 4: Check if turn rotated
    console.log('\n🧪 TEST 4: Turn Rotation');
    const newActivePlayer = players.find(p => p.isMyTurn);
    if (newActivePlayer) {
      console.log(`✅ Turn rotated to Player ${newActivePlayer.id}`);
    } else {
      console.log('❌ No active player found');
    }
    
    console.log('\n🎮 Gameplay test completed!');
    console.log('💡 Open http://localhost:5173 in your browser to continue playing manually');
    
  } catch (error) {
    console.error('❌ Gameplay test failed:', error);
  }
  
  // Keep connections alive
  console.log('\n🔄 Keeping connections alive for manual testing...');
}

simulateGameplay().catch(console.error);
