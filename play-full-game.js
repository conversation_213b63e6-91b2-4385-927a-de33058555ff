// Complete Scout game simulation - 3 players from start to finish
const io = require('socket.io-client');

async function playFullGame() {
  console.log('🎮 PLAYING COMPLETE SCOUT GAME - 3 PLAYERS');
  console.log('=' .repeat(50));
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null,
      hand: [],
      isMyTurn: false,
      activeSet: null,
      gameState: null,
      roundState: null
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // === GAME SETUP ===
    console.log('\n📝 GAME SETUP');
    console.log('-'.repeat(30));
    
    // Player 1 creates room
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    console.log(`✅ Room created: ${roomData.roomId}`);
    
    // Players 2 and 3 join
    for (let i = 1; i < 3; i++) {
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
      console.log(`✅ ${players[i].name} joined room`);
    }
    
    // Set up event listeners
    players.forEach((player, index) => {
      player.socket.on('roundState', (roundState) => {
        player.hand = roundState.you?.hand || [];
        player.isMyTurn = roundState.activePlayer === player.playerId;
        player.activeSet = roundState.activeSet;
        player.roundState = roundState;
      });
      
      player.socket.on('roomState', (gameState) => {
        player.gameState = gameState;
      });
    });
    
    // Start the game (3 rounds for 3 players)
    const startResult = await new Promise((resolve) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, (success) => {
        resolve(success);
      });
    });
    
    if (!startResult) {
      throw new Error('Failed to start game');
    }
    
    console.log('✅ Game started - 3 rounds to play!');
    
    // Wait for initial state
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // === PLAY ALL ROUNDS ===
    for (let round = 1; round <= 3; round++) {
      console.log(`\n🎯 ROUND ${round}`);
      console.log('-'.repeat(30));
      
      // Show initial hands
      console.log('\n📋 Starting hands:');
      players.forEach((player, index) => {
        if (player.hand.length > 0) {
          const handStr = player.hand.map(c => {
            const [higher, lower] = c.cardId.split('-').map(Number);
            return `${higher}|${lower}`;
          }).join(', ');
          console.log(`${player.name}: ${handStr}`);
        }
      });
      
      // Play the round
      let turnCount = 0;
      let maxTurns = 50; // Safety limit
      
      while (turnCount < maxTurns) {
        const currentPlayer = players.find(p => p.isMyTurn);
        if (!currentPlayer) break;
        
        // Check if round ended
        if (currentPlayer.roundState?.phase === 'ended') {
          console.log('\n🏁 Round ended!');
          break;
        }
        
        turnCount++;
        console.log(`\nTurn ${turnCount}: ${currentPlayer.name}'s turn`);
        
        // Choose initial orientation if needed (first turn)
        if (turnCount === 1) {
          await new Promise((resolve) => {
            currentPlayer.socket.emit('chooseInitialHandOrientation', { orientation: 'a' }, resolve);
          });
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        // Decide what to do based on game state
        let actionTaken = false;
        
        // If there's an active set, try to scout or beat it
        if (currentPlayer.activeSet && currentPlayer.activeSet.cards.length > 0) {
          // 70% chance to scout, 30% chance to try to beat
          if (Math.random() < 0.7) {
            // Try to scout
            const scoutResult = await new Promise((resolve) => {
              currentPlayer.socket.emit('scout', { 
                edge: Math.random() < 0.5 ? 'left' : 'right',
                insertAt: Math.floor(Math.random() * (currentPlayer.hand.length + 1)),
                orientation: 'a'
              }, (ok, error) => {
                resolve({ ok, error });
              });
            });
            
            if (scoutResult.ok) {
              console.log(`  ✅ ${currentPlayer.name} scouted from active set`);
              actionTaken = true;
            } else {
              console.log(`  ❌ Scout failed: ${scoutResult.error}`);
            }
          } else {
            // Try to beat the active set with a single card
            if (currentPlayer.hand.length > 0) {
              const showResult = await new Promise((resolve) => {
                currentPlayer.socket.emit('show', { 
                  start: 0, 
                  endInclusive: 0 
                }, (ok, error) => {
                  resolve({ ok, error });
                });
              });
              
              if (showResult.ok) {
                console.log(`  ✅ ${currentPlayer.name} showed a card`);
                actionTaken = true;
              } else {
                console.log(`  ❌ Show failed: ${showResult.error}`);
              }
            }
          }
        }
        
        // If no action taken yet, try to show cards or pass
        if (!actionTaken) {
          if (currentPlayer.hand.length > 0 && Math.random() < 0.6) {
            // Try to show 1-3 cards
            const numCards = Math.min(
              Math.floor(Math.random() * 3) + 1,
              currentPlayer.hand.length
            );
            
            const showResult = await new Promise((resolve) => {
              currentPlayer.socket.emit('show', { 
                start: 0, 
                endInclusive: numCards - 1
              }, (ok, error) => {
                resolve({ ok, error });
              });
            });
            
            if (showResult.ok) {
              console.log(`  ✅ ${currentPlayer.name} showed ${numCards} card(s)`);
              actionTaken = true;
            } else {
              console.log(`  ❌ Show failed: ${showResult.error}`);
            }
          }
          
          // If still no action, pass
          if (!actionTaken) {
            const passResult = await new Promise((resolve) => {
              currentPlayer.socket.emit('pass', {}, (ok, error) => {
                resolve({ ok, error });
              });
            });
            
            if (passResult.ok) {
              console.log(`  ✅ ${currentPlayer.name} passed`);
              actionTaken = true;
            } else {
              console.log(`  ❌ Pass failed: ${passResult.error}`);
            }
          }
        }
        
        // Wait for state update
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Check if someone won the round (empty hand)
        const winner = players.find(p => p.hand.length === 0);
        if (winner) {
          console.log(`\n🏆 ${winner.name} emptied their hand and won the round!`);
          break;
        }
      }
      
      // Complete the round
      console.log('\n📊 Completing round...');
      await new Promise((resolve) => {
        players[0].socket.emit('completeRound', {}, resolve);
      });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show round results
      console.log('\n📈 Round Results:');
      if (players[0].gameState) {
        const currentScores = players[0].gameState.scores;
        const roundScores = players[0].gameState.roundScores[round - 1] || {};
        
        players.forEach(player => {
          const roundScore = roundScores[player.playerId] || 0;
          const totalScore = currentScores[player.playerId] || 0;
          console.log(`${player.name}: +${roundScore} (Total: ${totalScore})`);
        });
      }
      
      // Start next round if not the last one
      if (round < 3) {
        console.log('\n🔄 Starting next round...');
        const nextRoundResult = await new Promise((resolve) => {
          players[0].socket.emit('startRound', { 
            playerCount: 3, 
            variant: 'standard' 
          }, (success) => {
            resolve(success);
          });
        });
        
        if (!nextRoundResult) {
          console.log('❌ Failed to start next round');
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    // === FINAL RESULTS ===
    console.log('\n🏆 FINAL GAME RESULTS');
    console.log('=' .repeat(50));
    
    if (players[0].gameState) {
      const finalScores = players[0].gameState.scores;
      const sortedPlayers = players
        .map(p => ({ name: p.name, score: finalScores[p.playerId] || 0 }))
        .sort((a, b) => b.score - a.score);
      
      console.log('\n🥇 Final Standings:');
      sortedPlayers.forEach((player, index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉';
        console.log(`${medal} ${index + 1}. ${player.name}: ${player.score} points`);
      });
      
      console.log(`\n🎉 ${sortedPlayers[0].name} wins the game!`);
      
      // Show round-by-round breakdown
      console.log('\n📊 Round-by-Round Scores:');
      const roundScores = players[0].gameState.roundScores;
      for (let r = 0; r < roundScores.length; r++) {
        console.log(`\nRound ${r + 1}:`);
        players.forEach(player => {
          const score = roundScores[r][player.playerId] || 0;
          console.log(`  ${player.name}: ${score}`);
        });
      }
    }
    
    console.log('\n🎮 COMPLETE SCOUT GAME FINISHED!');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ Game failed:', error);
  }
  
  // Clean up connections
  players.forEach(player => player.socket.disconnect());
}

playFullGame().catch(console.error);
