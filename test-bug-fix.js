// Test script to verify the scoring bug fix
const io = require('socket.io-client');

async function testBugFix() {
  console.log('🐛 Testing Scout Scoring Bug Fix - Player 3 beats Player 1\'s set');
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null,
      hand: [],
      isMyTurn: false,
      facedownCount: 0
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Set up game
    console.log('📝 Setting up 3-player game...');
    
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    
    // Players 2 and 3 join
    for (let i = 1; i < 3; i++) {
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room: ${response?.error || 'Unknown error'}`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
    }
    
    // Start round
    const startResult = await new Promise((resolve) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, resolve);
    });
    
    if (!startResult) {
      throw new Error('Failed to start round');
    }
    
    console.log('✅ Game setup complete!');
    
    // Set up event listeners to track state
    players.forEach((player, index) => {
      player.socket.on('roundState', (roundState) => {
        player.hand = roundState.you?.hand || [];
        player.isMyTurn = roundState.activePlayer === player.playerId;
        player.facedownCount = roundState.players.find(p => p.id === player.playerId)?.facedownCount || 0;
        
        console.log(`🎯 Player ${index + 1} state:`, {
          handSize: player.hand.length,
          isMyTurn: player.isMyTurn,
          facedownCount: player.facedownCount,
          phase: roundState.phase
        });
      });
    });
    
    // Wait for initial state
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Step 1: Player 1 chooses orientation and shows a set
    console.log('\n🧪 STEP 1: Player 1 shows a set');
    let currentPlayer = players.find(p => p.isMyTurn);
    if (currentPlayer && currentPlayer.id === 1) {
      // Choose orientation
      await new Promise((resolve) => {
        currentPlayer.socket.emit('chooseInitialHandOrientation', { orientation: 'a' }, resolve);
      });
      
      // Show first card
      const showResult = await new Promise((resolve) => {
        currentPlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 0 
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      
      console.log(`Player 1 show result: ${showResult.ok ? '✅ Success' : `❌ Failed: ${showResult.error}`}`);
      
      if (showResult.ok) {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Record Player 1's facedown count after showing
        const player1FacedownAfterShow = players[0].facedownCount;
        console.log(`Player 1 facedown count after showing: ${player1FacedownAfterShow}`);
        
        // Step 2: Player 2 passes to get to Player 3
        console.log('\n🧪 STEP 2: Player 2 passes');
        currentPlayer = players.find(p => p.isMyTurn);
        if (currentPlayer && currentPlayer.id === 2) {
          const passResult = await new Promise((resolve) => {
            currentPlayer.socket.emit('pass', {}, (ok, error) => {
              resolve({ ok, error });
            });
          });
          console.log(`Player 2 pass result: ${passResult.ok ? '✅ Success' : `❌ Failed: ${passResult.error}`}`);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Step 3: Player 3 tries to beat Player 1's set
        console.log('\n🧪 STEP 3: Player 3 attempts to beat Player 1\'s set');
        currentPlayer = players.find(p => p.isMyTurn);
        if (currentPlayer && currentPlayer.id === 3) {
          // Record counts before Player 3's move
          const player1FacedownBefore = players[0].facedownCount;
          const player3FacedownBefore = players[2].facedownCount;
          
          console.log(`Before Player 3's move:`);
          console.log(`  Player 1 facedown: ${player1FacedownBefore}`);
          console.log(`  Player 3 facedown: ${player3FacedownBefore}`);
          
          // Try to find a card that beats Player 1's set
          let beatResult = { ok: false };
          for (let i = 0; i < Math.min(currentPlayer.hand.length, 10); i++) {
            beatResult = await new Promise((resolve) => {
              currentPlayer.socket.emit('show', { 
                start: i, 
                endInclusive: i 
              }, (ok, error) => {
                resolve({ ok, error });
              });
            });
            
            if (beatResult.ok) {
              console.log(`✅ Player 3 successfully beat Player 1's set with card at position ${i}!`);
              break;
            }
          }
          
          if (beatResult.ok) {
            // Wait for state update
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Check the final counts - this is the bug fix test!
            const player1FacedownAfter = players[0].facedownCount;
            const player3FacedownAfter = players[2].facedownCount;
            
            console.log(`\nAfter Player 3's move:`);
            console.log(`  Player 1 facedown: ${player1FacedownAfter}`);
            console.log(`  Player 3 facedown: ${player3FacedownAfter}`);
            
            // THE BUG FIX VERIFICATION:
            if (player3FacedownAfter > player3FacedownBefore) {
              console.log('✅ BUG FIX VERIFIED: Player 3 correctly received the beaten cards!');
            } else {
              console.log('❌ BUG STILL EXISTS: Player 3 did not receive the beaten cards!');
            }
            
            if (player1FacedownAfter === player1FacedownBefore) {
              console.log('✅ CORRECT: Player 1 did not incorrectly gain cards from their own beaten set');
            } else {
              console.log('❌ BUG: Player 1 incorrectly gained cards from their own beaten set');
            }
            
          } else {
            console.log('⚠️  Player 3 could not beat Player 1\'s set with available cards');
          }
        }
      }
    }
    
    console.log('\n🎮 Bug fix test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Clean up
    players.forEach(player => player.socket.disconnect());
    process.exit(0);
  }
}

testBugFix();
