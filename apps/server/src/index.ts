import express from 'express';
import http from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import { nanoid } from 'nanoid/non-secure';
import type { ClientToServerEvents, ServerToClientEvents, PlayerId, RoomId } from '@scout/protocol';
import { Engine } from '@scout/game/src/engine';

const PORT = process.env.PORT ? Number(process.env.PORT) : 4000;

const app = express();
app.use(cors());
const server = http.createServer(app);
const io = new Server<ClientToServerEvents, ServerToClientEvents>(server, {
  cors: { origin: '*' },
});

type Room = {
  id: RoomId;
  players: Map<PlayerId, { id: PlayerId; name: string; token: string; socketId?: string }>;
  order: PlayerId[];
  scores: Record<PlayerId, number>; // cumulative scores across all rounds
  roundScores: Record<PlayerId, number>[]; // scores for each completed round
  currentRound: number;
  totalRounds: number;
  engine?: Engine;
};

const rooms = new Map<RoomId, Room>();

io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  let joinedRoomId: RoomId | null = null;
  let playerId: PlayerId | null = null;

  socket.on('createRoom', ({ name }, cb) => {
    const roomId = nanoid(6);
    const pid = nanoid(8);
    const token = nanoid(12);
    const room: Room = {
      id: roomId,
      players: new Map(),
      order: [],
      scores: {},
      roundScores: [],
      currentRound: 0,
      totalRounds: 0
    };
    room.players.set(pid, { id: pid, name, token, socketId: socket.id });
    room.order = [pid];
    room.scores[pid] = 0;
    rooms.set(roomId, room);
    joinedRoomId = roomId;
    playerId = pid;
    socket.join(roomId);
    cb({ roomId, playerId: pid, token });
  });

  socket.on('joinRoom', ({ roomId, name }, cb) => {
    const room = rooms.get(roomId);
    if (!room) return cb({ ok: false, error: 'Room not found' });
    const pid = nanoid(8);
    const token = nanoid(12);
    room.players.set(pid, { id: pid, name, token, socketId: socket.id });
    room.order.push(pid);
    room.scores[pid] = 0;
    joinedRoomId = roomId;
    playerId = pid;
    socket.join(roomId);
    cb({ ok: true, playerId: pid, token });
    emitRoomState(roomId);
  });

  socket.on('startRound', ({ playerCount, variant }, cb) => {
    console.log(`startRound called: playerCount=${playerCount}, joinedRoomId=${joinedRoomId}, playerId=${playerId}`);
    if (!joinedRoomId || !playerId) {
      console.log('startRound failed: no room or player ID');
      return cb(false);
    }
    const room = rooms.get(joinedRoomId)!;
    const ids = room.order;
    console.log(`Room has ${ids.length} players, requested ${playerCount}`);
    if (ids.length !== playerCount) {
      console.log('startRound failed: player count mismatch');
      return cb(false);
    }

    // Initialize game if this is the first round
    if (room.currentRound === 0) {
      // Set total rounds: 2 for 2-player variant, otherwise as many rounds as players
      room.totalRounds = playerCount === 2 ? 2 : playerCount;
      for (const id of ids) {
        room.scores[id] = 0;
      }
    }

    room.currentRound += 1;
    const names: Record<PlayerId, string> = {};
    for (const id of ids) names[id] = room.players.get(id)!.name;
    room.engine = new Engine({ playerIds: ids, playerNames: names, playerCount: playerCount as any, variant: variant ?? (ids.length === 2 ? 'two-player' : 'standard') });
    console.log('startRound success: engine created');
    cb(true);
    emitRoundState(joinedRoomId);
  });

  socket.on('chooseInitialHandOrientation', ({ orientation }, cb) => {
    if (!joinedRoomId || !playerId) return cb(false);
    const room = rooms.get(joinedRoomId)!;
    room.engine?.chooseInitialHandOrientation(playerId, orientation);
    cb(true);
    emitRoundState(joinedRoomId);
  });

  socket.on('show', ({ start, endInclusive, orientations }, cb) => {
    if (!joinedRoomId || !playerId) return cb(false);
    const room = rooms.get(joinedRoomId)!;
    const res = room.engine?.show(playerId, start, endInclusive, orientations);
    cb(res?.ok ?? false, res?.error);
    emitRoundState(joinedRoomId);
  });

  socket.on('scout', ({ edge, insertAt, orientation, useScoutAndShow }, cb) => {
    if (!joinedRoomId || !playerId) return cb(false);
    const room = rooms.get(joinedRoomId)!;
    const res = room.engine?.scout(playerId, edge, insertAt, orientation, useScoutAndShow);
    cb(res?.ok ?? false, (res as any)?.error);
    emitRoundState(joinedRoomId);
  });

  socket.on('pass', (_payload, cb) => {
    if (!joinedRoomId || !playerId) return cb(false);
    const room = rooms.get(joinedRoomId)!;
    const res = room.engine?.pass(playerId);
    cb(res?.ok ?? false, res?.error);
    emitRoundState(joinedRoomId);
  });

  socket.on('requestState', (_payload, cb) => {
    if (!joinedRoomId || !playerId) return cb(null);
    const room = rooms.get(joinedRoomId)!;
    const state = room.engine?.getStateFor(playerId) ?? null;
    cb(state);
  });

  socket.on('completeRound', (_payload, cb) => {
    if (!joinedRoomId || !playerId) return cb(false);
    const room = rooms.get(joinedRoomId)!;
    if (!room.engine || !room.engine.isRoundEnded()) return cb(false);

    const roundResult = room.engine.getRoundResult();
    if (!roundResult) return cb(false);

    // Add round scores to cumulative scores
    room.roundScores.push(roundResult.scores);
    for (const [pid, roundScore] of Object.entries(roundResult.scores)) {
      room.scores[pid] = (room.scores[pid] || 0) + roundScore;
    }

    // Clear the engine to prepare for next round
    room.engine = undefined;

    cb(true);
    emitRoomState(joinedRoomId);
  });
});

function emitRoomState(roomId: RoomId) {
  const room = rooms.get(roomId);
  if (!room) return;
  io.to(roomId).emit('roomState', {
    roomId: roomId,
    playerOrder: [...room.order],
    scores: { ...room.scores },
    roundScores: [...room.roundScores],
    currentRound: room.currentRound,
    totalRounds: room.totalRounds,
    round: room.engine ? room.engine.getPublicState() : null,
  });
}

function emitRoundState(roomId: RoomId) {
  const room = rooms.get(roomId);
  if (!room || !room.engine) return;

  // Send per-player state to each player individually
  for (const pid of room.order) {
    const player = room.players.get(pid);
    if (player?.socketId) {
      const socket = io.sockets.sockets.get(player.socketId);
      if (socket) {
        const playerState = room.engine.getStateFor(pid);
        socket.emit('roundState', playerState);
      }
    }
  }
}

server.listen(PORT, () => {
  console.log(`Server on http://localhost:${PORT}`);
});

