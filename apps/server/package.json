{"name": "@scout/server", "version": "0.1.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/index.js", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"cors": "^2.8.5", "express": "^4.19.2", "nanoid": "^5.0.7", "socket.io": "^4.7.5", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.30", "tsx": "^4.19.2", "typescript": "^5.5.4"}}