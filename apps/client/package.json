{"name": "@scout/client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.json && vite build", "preview": "vite preview", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@vitejs/plugin-react": "^4.7.0", "classnames": "^2.5.1", "framer-motion": "^12.23.22", "react": "^18.3.1", "react-dom": "^18.3.1", "socket.io-client": "^4.7.5", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "vite": "^5.4.6"}}