import { create } from 'zustand';
import type { 
  GameStatePublic, 
  RoundStateForPlayer, 
  PlayerId, 
  RoomId,
  CardInHand,
  Orientation 
} from '@scout/protocol';

export interface GameStore {
  // Connection state
  connected: boolean;
  roomId: RoomId | null;
  playerId: PlayerId | null;
  
  // Game state
  gameState: GameStatePublic | null;
  roundState: RoundStateForPlayer | null;
  
  // UI state
  selectedCards: number[]; // indices of selected cards in hand
  previewOrientations: Orientation[]; // orientations for selected cards
  scoutingMode: boolean;
  scoutEdge: 'left' | 'right' | null;
  scoutInsertIndex: number | null;
  
  // Actions
  setConnected: (connected: boolean) => void;
  setRoomId: (roomId: RoomId | null) => void;
  setPlayerId: (playerId: PlayerId | null) => void;
  setGameState: (gameState: GameStatePublic | null) => void;
  setRoundState: (roundState: RoundStateForPlayer | null) => void;
  
  // UI actions
  selectCard: (index: number) => void;
  deselectCard: (index: number) => void;
  clearSelection: () => void;
  setPreviewOrientation: (index: number, orientation: Orientation) => void;
  toggleCardOrientation: (index: number) => void;
  
  // Scouting actions
  enterScoutingMode: (edge: 'left' | 'right') => void;
  exitScoutingMode: () => void;
  setScoutInsertIndex: (index: number | null) => void;
  
  // Computed getters
  isMyTurn: () => boolean;
  canShow: () => boolean;
  canScout: () => boolean;
  canPass: () => boolean;
  getSelectedCardsRange: () => { start: number; end: number } | null;
}

export const useGameStore = create<GameStore>((set, get) => ({
  // Initial state
  connected: false,
  roomId: null,
  playerId: null,
  gameState: null,
  roundState: null,
  selectedCards: [],
  previewOrientations: [],
  scoutingMode: false,
  scoutEdge: null,
  scoutInsertIndex: null,
  
  // Connection actions
  setConnected: (connected) => set({ connected }),
  setRoomId: (roomId) => set({ roomId }),
  setPlayerId: (playerId) => set({ playerId }),
  setGameState: (gameState) => set({ gameState }),
  setRoundState: (roundState) => set({ roundState }),
  
  // UI actions
  selectCard: (index) => set((state) => {
    const selected = [...state.selectedCards];
    if (!selected.includes(index)) {
      selected.push(index);
      selected.sort((a, b) => a - b);
    }
    return { selectedCards: selected };
  }),
  
  deselectCard: (index) => set((state) => ({
    selectedCards: state.selectedCards.filter(i => i !== index)
  })),
  
  clearSelection: () => set({ 
    selectedCards: [], 
    previewOrientations: [],
    scoutingMode: false,
    scoutEdge: null,
    scoutInsertIndex: null
  }),
  
  setPreviewOrientation: (index, orientation) => set((state) => {
    const orientations = [...state.previewOrientations];
    orientations[index] = orientation;
    return { previewOrientations: orientations };
  }),
  
  toggleCardOrientation: (index) => set((state) => {
    const roundState = state.roundState;
    if (!roundState) return state;
    
    const card = roundState.you.hand[index];
    if (!card) return state;
    
    const currentOrientation = state.previewOrientations[index] || card.orientation;
    const newOrientation: Orientation = currentOrientation === 'a' ? 'b' : 'a';
    
    const orientations = [...state.previewOrientations];
    orientations[index] = newOrientation;
    return { previewOrientations: orientations };
  }),
  
  // Scouting actions
  enterScoutingMode: (edge) => set({ 
    scoutingMode: true, 
    scoutEdge: edge,
    selectedCards: [],
    previewOrientations: []
  }),
  
  exitScoutingMode: () => set({ 
    scoutingMode: false, 
    scoutEdge: null,
    scoutInsertIndex: null
  }),
  
  setScoutInsertIndex: (index) => set({ scoutInsertIndex: index }),
  
  // Computed getters
  isMyTurn: () => {
    const { roundState, playerId } = get();
    return roundState?.activePlayer === playerId;
  },
  
  canShow: () => {
    const { roundState, selectedCards, playerId } = get();
    return roundState?.activePlayer === playerId && 
           selectedCards.length > 0 && 
           roundState.phase === 'playing';
  },
  
  canScout: () => {
    const { roundState, playerId } = get();
    return !!(roundState?.activePlayer === playerId &&
              roundState?.activeSet &&
              roundState.phase === 'playing');
  },
  
  canPass: () => {
    const { roundState, playerId } = get();
    return roundState?.activePlayer === playerId && 
           roundState.phase === 'playing';
  },
  
  getSelectedCardsRange: () => {
    const { selectedCards } = get();
    if (selectedCards.length === 0) return null;
    
    // Check if selection is contiguous
    const sorted = [...selectedCards].sort((a, b) => a - b);
    for (let i = 1; i < sorted.length; i++) {
      if (sorted[i] !== sorted[i-1] + 1) {
        return null; // Not contiguous
      }
    }
    
    return { start: sorted[0], end: sorted[sorted.length - 1] };
  }
}));
