import { useState, useEffect } from 'react';
import { useGameStore } from '../store/gameStore';
import { socketService } from '../services/socketService';
import { soundService } from '../services/soundService';
import clsx from 'classnames';

export function RoundProgression() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const gameState = useGameStore(state => state.gameState);
  const roundState = useGameStore(state => state.roundState);
  const playerId = useGameStore(state => state.playerId);
  
  if (!gameState || !roundState) return null;
  
  const isRoundEnded = roundState.phase === 'ended';
  const isGameComplete = gameState.currentRound >= gameState.totalRounds && isRoundEnded;
  const canStartNextRound = isRoundEnded && !isGameComplete;

  // Play sounds for round end and game completion
  useEffect(() => {
    if (isRoundEnded && roundState.result) {
      soundService.roundEnd();
    }
  }, [isRoundEnded, roundState.result]);

  useEffect(() => {
    if (isGameComplete) {
      soundService.gameWin();
    }
  }, [isGameComplete]);
  
  const handleCompleteRound = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const success = await socketService.completeRound();
      if (!success) {
        setError('Failed to complete round');
      }
    } catch (error) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };
  
  const handleStartNextRound = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const success = await socketService.startRound(
        gameState.playerOrder.length,
        gameState.playerOrder.length === 2 ? 'two-player' : 'standard'
      );
      if (!success) {
        setError('Failed to start next round');
      }
    } catch (error) {
      setError('Network error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white/5 rounded-xl p-6 border border-white/10">
      {/* Round status */}
      <div className="text-center mb-6">
        <div className="text-3xl font-bold text-brand-400 mb-2">
          Round {gameState.currentRound}
        </div>
        <div className="text-slate-400">
          of {gameState.totalRounds} rounds
        </div>
        
        {/* Progress bar */}
        <div className="mt-4 w-full bg-slate-700 rounded-full h-3">
          <div 
            className="bg-brand-500 h-3 rounded-full transition-all duration-1000 ease-out"
            style={{ width: `${(gameState.currentRound / gameState.totalRounds) * 100}%` }}
          />
        </div>
        <div className="text-xs text-slate-500 mt-2">
          {Math.round((gameState.currentRound / gameState.totalRounds) * 100)}% complete
        </div>
      </div>
      
      {/* Round end summary */}
      {isRoundEnded && roundState.result && (
        <div className="mb-6 p-4 bg-slate-800 rounded-lg">
          <h3 className="font-semibold mb-3 text-center">Round {gameState.currentRound} Results</h3>
          
          <div className="text-center mb-4">
            <div className="text-sm text-slate-400">
              Round ended by: <span className="text-slate-300">
                {roundState.result.reason === 'empty_hand' ? 'Empty hand' : 'All players passed'}
              </span>
            </div>
            {roundState.result.winner && (
              <div className="text-sm text-slate-400 mt-1">
                Winner: <span className="text-green-400">
                  {roundState.players.find(p => p.id === roundState.result?.winner)?.name}
                </span>
              </div>
            )}
          </div>
          
          {/* Round scores */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {gameState.playerOrder.map(pid => {
              const player = roundState.players.find(p => p.id === pid);
              const roundScore = roundState.result?.scores[pid] || 0;
              const isMe = pid === playerId;
              const isWinner = pid === roundState.result?.winner;
              
              return (
                <div 
                  key={pid}
                  className={clsx(
                    "flex justify-between items-center p-2 rounded",
                    isMe && "bg-brand-500/10",
                    isWinner && "bg-green-500/10"
                  )}
                >
                  <span className={clsx(
                    "text-sm",
                    isMe && "text-brand-300",
                    isWinner && "text-green-300"
                  )}>
                    {player?.name}
                    {isMe && " (You)"}
                    {isWinner && " 👑"}
                  </span>
                  <span className={clsx(
                    "font-mono font-semibold",
                    roundScore > 0 ? "text-green-400" : 
                    roundScore < 0 ? "text-red-400" : "text-slate-400"
                  )}>
                    {roundScore > 0 ? '+' : ''}{roundScore}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Game completion */}
      {isGameComplete && (
        <div className="mb-6 p-6 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
          <div className="text-2xl font-bold text-green-300 mb-2">
            🎉 Game Complete! 🎉
          </div>
          
          {/* Final standings */}
          <div className="mt-4">
            <h4 className="font-semibold mb-3">Final Standings</h4>
            <div className="space-y-2">
              {[...gameState.playerOrder]
                .sort((a, b) => (gameState.scores[b] || 0) - (gameState.scores[a] || 0))
                .map((pid, rank) => {
                  const player = roundState.players.find(p => p.id === pid);
                  const totalScore = gameState.scores[pid] || 0;
                  const isMe = pid === playerId;
                  
                  return (
                    <div 
                      key={pid}
                      className={clsx(
                        "flex justify-between items-center p-2 rounded",
                        isMe && "bg-brand-500/10",
                        rank === 0 && "bg-green-500/10"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <span className={clsx(
                          "font-semibold",
                          rank === 0 && "text-green-300",
                          isMe && !rank && "text-brand-300"
                        )}>
                          #{rank + 1}
                        </span>
                        <span className={clsx(
                          isMe && "text-brand-300",
                          rank === 0 && "text-green-300"
                        )}>
                          {player?.name}
                          {isMe && " (You)"}
                        </span>
                        {rank === 0 && <span>🥇</span>}
                        {rank === 1 && <span>🥈</span>}
                        {rank === 2 && <span>🥉</span>}
                      </div>
                      <span className={clsx(
                        "font-bold text-lg",
                        rank === 0 && "text-green-300",
                        isMe && !rank && "text-brand-300"
                      )}>
                        {totalScore}
                      </span>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}
      
      {/* Action buttons */}
      <div className="flex justify-center gap-3">
        {isRoundEnded && !gameState.roundScores.includes(roundState.result?.scores || {}) && (
          <button
            className="px-6 py-3 bg-brand-600 hover:bg-brand-500 text-white rounded-lg font-medium transition-all disabled:opacity-50"
            onClick={handleCompleteRound}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Complete Round'}
          </button>
        )}
        
        {canStartNextRound && gameState.roundScores.length === gameState.currentRound && (
          <button
            className="px-6 py-3 bg-green-600 hover:bg-green-500 text-white rounded-lg font-medium transition-all disabled:opacity-50"
            onClick={handleStartNextRound}
            disabled={loading}
          >
            {loading ? 'Starting...' : `Start Round ${gameState.currentRound + 1}`}
          </button>
        )}
        
        {isGameComplete && (
          <button
            className="px-6 py-3 bg-purple-600 hover:bg-purple-500 text-white rounded-lg font-medium transition-all"
            onClick={() => window.location.reload()}
          >
            New Game
          </button>
        )}
      </div>
      
      {error && (
        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm text-center">
          {error}
          <button 
            className="ml-2 text-red-300 hover:text-red-200"
            onClick={() => setError(null)}
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
}
