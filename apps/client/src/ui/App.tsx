import { useEffect, useState } from 'react';
import type { RoomId } from '@scout/protocol';
import { useGameStore } from '../store/gameStore';
import { socketService } from '../services/socketService';
import { GameTable } from './GameTable';

export function App() {
  const [roomId, setRoomId] = useState<RoomId | null>(null);
  const [name, setName] = useState('');
  const [joined, setJoined] = useState(false);
  const [playerCount, setPlayerCount] = useState(3);

  const gameState = useGameStore(state => state.gameState);
  const roundState = useGameStore(state => state.roundState);

  useEffect(() => {
    // Update local roomId when game state changes
    if (gameState?.roomId && !roomId) {
      setRoomId(gameState.roomId);
    }
  }, [gameState, roomId]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      <header className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
        <h1 className="text-xl font-bold tracking-wide"><span className="text-brand-400">SCOUT</span> — Web</h1>
        <div className="text-sm opacity-70">Room: {roomId ?? '—'}</div>
      </header>
      <main className="max-w-6xl mx-auto p-6">
        {!joined ? (
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white/5 p-6 rounded-xl border border-white/10">
              <h2 className="font-semibold text-lg mb-4">Create Room</h2>
              <div className="space-y-3">
                <input className="w-full px-3 py-2 bg-white/10 rounded-md outline-none focus:ring-2 ring-brand-600" placeholder="Your name" value={name} onChange={(e)=>setName(e.target.value)} />
                <button className="px-4 py-2 bg-brand-600 hover:bg-brand-500 rounded-md" onClick={async ()=>{
                  try {
                    const response = await socketService.createRoom(name || 'Host');
                    setJoined(true);
                    setRoomId(response.roomId);
                  } catch (error) {
                    console.error('Failed to create room:', error);
                  }
                }}>Create</button>
              </div>
            </div>
            <div className="bg-white/5 p-6 rounded-xl border border-white/10">
              <h2 className="font-semibold text-lg mb-4">Join Room</h2>
              <div className="space-y-3">
                <input className="w-full px-3 py-2 bg-white/10 rounded-md outline-none focus:ring-2 ring-brand-600" placeholder="Room ID" value={roomId ?? ''} onChange={(e)=>setRoomId(e.target.value as any)} />
                <input className="w-full px-3 py-2 bg-white/10 rounded-md outline-none focus:ring-2 ring-brand-600" placeholder="Your name" value={name} onChange={(e)=>setName(e.target.value)} />
                <button className="px-4 py-2 bg-brand-600 hover:bg-brand-500 rounded-md" onClick={async ()=>{
                  if (!roomId) return;
                  try {
                    await socketService.joinRoom(roomId, name || 'Guest');
                    setJoined(true);
                  } catch (error) {
                    console.error('Failed to join room:', error);
                  }
                }}>Join</button>
              </div>
            </div>
          </div>
        ) : roundState ? (
          <GameTable />
        ) : (
          <Lobby roomId={roomId!} />
        )}
      </main>
    </div>
  );
}

function Lobby({ roomId }: { roomId: RoomId }) {
  const [count, setCount] = useState(3);
  const [message, setMessage] = useState('');

  const gameState = useGameStore(state => state.gameState);
  const actualPlayerCount = gameState?.playerOrder.length || 0;

  return (
    <div className="bg-white/5 p-6 rounded-xl border border-white/10">
      <h2 className="font-semibold text-lg mb-4">Lobby</h2>
      <div className="mb-4">
        <p>Players in room: {actualPlayerCount}</p>
      </div>
      <div className="flex items-center gap-3">
        <label>Players:</label>
        <select className="bg-white/10 rounded px-2 py-1" value={count} onChange={(e)=>setCount(Number(e.target.value))}>
          <option value={2}>2</option>
          <option value={3}>3</option>
          <option value={4}>4</option>
          <option value={5}>5</option>
        </select>
        <button className="ml-auto px-4 py-2 bg-brand-600 hover:bg-brand-500 rounded" onClick={async ()=>{
          setMessage('Starting round...');
          try {
            const success = await socketService.startRound(count, count === 2 ? 'two-player' : 'standard');
            if (success) {
              setMessage('Round started successfully!');
            } else {
              setMessage(`Failed to start round. Make sure you have exactly ${count} players in the room.`);
            }
          } catch (error) {
            setMessage('Error starting round.');
          }
        }}>Start Round</button>
      </div>
      {message && (
        <div className="mt-4 p-3 bg-white/10 rounded">
          {message}
        </div>
      )}
    </div>
  );
}

