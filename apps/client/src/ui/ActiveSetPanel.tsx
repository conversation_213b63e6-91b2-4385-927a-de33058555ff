import { useGameStore } from '../store/gameStore';
import { CardVisual } from './Card';
import clsx from 'classnames';

export function ActiveSetPanel() {
  const roundState = useGameStore(state => state.roundState);
  const scoutingMode = useGameStore(state => state.scoutingMode);
  const scoutEdge = useGameStore(state => state.scoutEdge);
  const enterScoutingMode = useGameStore(state => state.enterScoutingMode);
  const exitScoutingMode = useGameStore(state => state.exitScoutingMode);
  const isMyTurn = useGameStore(state => state.isMyTurn);
  const canScout = useGameStore(state => state.canScout);
  
  if (!roundState) return null;
  
  const activeSet = roundState.activeSet;
  const unbeatenPlayer = roundState.players.find(p => p.id === roundState.unbeatenSince);
  
  if (!activeSet) {
    return (
      <div className="bg-white/5 rounded-xl p-8 border border-white/10 text-center">
        <div className="text-slate-400 mb-2">No active set</div>
        <div className="text-sm text-slate-500">
          {isMyTurn() ? "You can show any valid set to start" : "Waiting for a player to show a set"}
        </div>
      </div>
    );
  }
  
  const getCardValue = (cardId: string, orientation: 'a' | 'b') => {
    const [higher, lower] = cardId.split('-').map(Number);
    return orientation === 'a' ? higher : lower;
  };
  
  const handleScoutEdge = (edge: 'left' | 'right') => {
    if (scoutingMode && scoutEdge === edge) {
      exitScoutingMode();
    } else {
      enterScoutingMode(edge);
    }
  };

  return (
    <div className="bg-white/5 rounded-xl p-6 border border-white/10">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold">Active Set</h2>
          <div className="text-sm text-slate-400">
            {activeSet.type === 'kind' ? 'Kind' : 'Run'} of {activeSet.length} 
            {unbeatenPlayer && (
              <span className="ml-2">by {unbeatenPlayer.name}</span>
            )}
          </div>
        </div>
        
        {canScout() && (
          <div className="flex gap-2">
            <button
              className={clsx(
                "px-3 py-2 rounded-lg text-sm font-medium transition-all",
                scoutingMode && scoutEdge === 'left'
                  ? "bg-amber-500 text-white shadow-lg"
                  : "bg-amber-500/20 text-amber-400 hover:bg-amber-500/30"
              )}
              onClick={() => handleScoutEdge('left')}
            >
              Scout Left
            </button>
            <button
              className={clsx(
                "px-3 py-2 rounded-lg text-sm font-medium transition-all",
                scoutingMode && scoutEdge === 'right'
                  ? "bg-amber-500 text-white shadow-lg"
                  : "bg-amber-500/20 text-amber-400 hover:bg-amber-500/30"
              )}
              onClick={() => handleScoutEdge('right')}
            >
              Scout Right
            </button>
          </div>
        )}
      </div>
      
      <div className="relative">
        {/* Scout edge indicators */}
        {scoutingMode && (
          <>
            <div 
              className={clsx(
                "absolute left-0 top-0 bottom-0 w-2 rounded-l-lg transition-all z-10",
                scoutEdge === 'left' 
                  ? "bg-amber-400 shadow-lg shadow-amber-400/50" 
                  : "bg-amber-400/30"
              )}
            />
            <div 
              className={clsx(
                "absolute right-0 top-0 bottom-0 w-2 rounded-r-lg transition-all z-10",
                scoutEdge === 'right' 
                  ? "bg-amber-400 shadow-lg shadow-amber-400/50" 
                  : "bg-amber-400/30"
              )}
            />
          </>
        )}
        
        {/* Cards */}
        <div className="flex gap-2 justify-center">
          {activeSet.cards.map((playedCard, index) => {
            const [higher, lower] = playedCard.cardId.split('-').map(Number);
            const orientation = playedCard.value === higher ? 'a' : 'b';
            const isEdge = index === 0 || index === activeSet.cards.length - 1;
            const isScoutTarget = scoutingMode && (
              (scoutEdge === 'left' && index === 0) ||
              (scoutEdge === 'right' && index === activeSet.cards.length - 1)
            );
            
            return (
              <div
                key={`${playedCard.cardId}-${index}`}
                className={clsx(
                  "transition-all duration-200",
                  isScoutTarget && "transform scale-105 ring-2 ring-amber-400 shadow-lg shadow-amber-400/25",
                  scoutingMode && isEdge && !isScoutTarget && "opacity-50"
                )}
              >
                <CardVisual
                  valueTop={higher}
                  valueBottom={lower}
                  orientation={orientation}
                  className="shadow-md"
                />
                
                {/* Value indicator */}
                <div className="text-center mt-2 text-xs text-slate-400">
                  {playedCard.value}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* Set info */}
        <div className="mt-4 text-center text-sm text-slate-400">
          {activeSet.type === 'kind' ? (
            `All ${activeSet.cards[0].value}s`
          ) : (
            `${Math.min(...activeSet.cards.map(c => c.value))} to ${Math.max(...activeSet.cards.map(c => c.value))}`
          )}
        </div>
      </div>
      
      {scoutingMode && (
        <div className="mt-4 p-3 bg-amber-500/10 rounded-lg border border-amber-500/20">
          <div className="text-sm text-amber-300">
            Scouting {scoutEdge} edge • Choose where to insert the card in your hand
          </div>
          <button
            className="mt-2 text-xs text-slate-400 hover:text-slate-300"
            onClick={exitScoutingMode}
          >
            Cancel scouting
          </button>
        </div>
      )}
    </div>
  );
}
