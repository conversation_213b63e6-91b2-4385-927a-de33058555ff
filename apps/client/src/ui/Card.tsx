import clsx from 'classnames';
import { motion } from 'framer-motion';

export type CardVisualProps = {
  valueTop: number;
  valueBottom: number;
  orientation?: 'a'|'b';
  selected?: boolean;
  className?: string;
  animate?: boolean;
  onClick?: () => void;
  onDoubleClick?: () => void;
};

// Procedural SVG card. Orientation 'a' shows valueTop on the left/top; 'b' rotates values.
export function CardVisual({
  valueTop,
  valueBottom,
  orientation='a',
  selected=false,
  className,
  animate=true,
  onClick,
  onDoubleClick
}: CardVisualProps) {
  const [left, right] = orientation === 'a' ? [valueTop, valueBottom] : [valueBottom, valueTop];
  const palette = pickPalette(Math.max(valueTop, valueBottom));

  const cardVariants = {
    initial: { scale: 1, rotateY: 0 },
    hover: { scale: 1.05, transition: { duration: 0.2 } },
    selected: {
      scale: 1.1,
      y: -8,
      boxShadow: '0 10px 25px rgba(0,0,0,0.3)',
      transition: { duration: 0.2 }
    },
    flip: {
      rotateY: [0, 90, 0],
      transition: { duration: 0.6 }
    },
    tap: { scale: 0.95, transition: { duration: 0.1 } }
  };

  return (
    <motion.div
      className={clsx(
        'relative w-[72px] h-[108px] rounded-xl shadow-md overflow-hidden border cursor-pointer',
        selected ? 'ring-2 ring-brand-400 border-brand-400' : 'border-white/10',
        className
      )}
      variants={animate ? cardVariants : undefined}
      initial={animate ? "initial" : undefined}
      animate={selected ? "selected" : "initial"}
      whileHover={animate && !selected ? "hover" : undefined}
      whileTap={animate ? "tap" : undefined}
      onClick={onClick}
      onDoubleClick={onDoubleClick}
      layout
    >
      <svg width="100%" height="100%" viewBox="0 0 72 108" preserveAspectRatio="none">
        <defs>
          <linearGradient id={`grad-${valueTop}-${valueBottom}`} x1="0" x2="1" y1="0" y2="1">
            <stop offset="0%" stopColor={palette[0]} />
            <stop offset="100%" stopColor={palette[1]} />
          </linearGradient>
        </defs>
        <rect x="0" y="0" width="72" height="108" fill={`url(#grad-${valueTop}-${valueBottom})`} rx="12" />
        <motion.text
          x="6"
          y="14"
          fill="#111827"
          fontSize="12"
          fontWeight="800"
          animate={orientation === 'a' ? { opacity: 1 } : { opacity: 0.7 }}
        >
          {left}
        </motion.text>
        <motion.text
          x="60"
          y="102"
          fill="#111827"
          fontSize="12"
          fontWeight="800"
          textAnchor="end"
          animate={orientation === 'a' ? { opacity: 0.7 } : { opacity: 1 }}
        >
          {right}
        </motion.text>
      </svg>
    </motion.div>
  );
}

function pickPalette(n: number): [string,string] {
  const ramps: [string,string][] = [
    ['#fde68a', '#f59e0b'],
    ['#86efac', '#16a34a'],
    ['#93c5fd', '#1d4ed8'],
    ['#c7d2fe', '#7c3aed'],
    ['#fda4af', '#be123c'],
    ['#a7f3d0', '#059669'],
    ['#fbcfe8', '#db2777'],
    ['#fca5a5', '#ef4444'],
    ['#99f6e4', '#0891b2'],
    ['#fde68a', '#b45309'],
  ];
  return ramps[(n-1) % ramps.length];
}

