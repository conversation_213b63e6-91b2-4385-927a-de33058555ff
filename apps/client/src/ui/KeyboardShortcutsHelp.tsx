import { useState } from 'react';
import clsx from 'classnames';

export function KeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Help button */}
      <button
        className="fixed bottom-4 left-4 bg-slate-700 hover:bg-slate-600 text-white p-3 rounded-full shadow-lg transition-all z-50"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Show keyboard shortcuts help"
        title="Keyboard shortcuts (? or H)"
      >
        ❓
      </button>
      
      {/* Help modal */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
          role="dialog"
          aria-modal="true"
          aria-labelledby="shortcuts-title"
        >
          <div className="bg-slate-900 rounded-xl border border-white/10 max-w-2xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 id="shortcuts-title" className="text-xl font-bold">Keyboard Shortcuts</h2>
              <button
                className="text-slate-400 hover:text-white"
                onClick={() => setIsOpen(false)}
                aria-label="Close help"
              >
                ✕
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">
                {/* Card Selection */}
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-brand-400">Card Selection</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Select/deselect cards</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">1-9, 0</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Navigate through hand</span>
                      <div className="space-x-1">
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">←</kbd>
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">→</kbd>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Select all cards</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">Ctrl/Cmd + A</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Flip card orientation</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">F</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Clear selection</span>
                      <div className="space-x-1">
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">C</kbd>
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">Esc</kbd>
                      </div>
                    </div>
                  </div>
                </section>

                {/* Actions */}
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-green-400">Actions</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Show selected cards</span>
                      <div className="space-x-1">
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">S</kbd>
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">Enter</kbd>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Pass turn</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">P</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Scout left edge</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">L</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Scout right edge</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">R</kbd>
                    </div>
                  </div>
                </section>

                {/* Scouting Mode */}
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-amber-400">Scouting Mode</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Set insert position</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">1-9, 0</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Navigate positions</span>
                      <div className="space-x-1">
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">←</kbd>
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">→</kbd>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Confirm scout</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">Enter</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-300">Cancel scouting</span>
                      <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">Esc</kbd>
                    </div>
                  </div>
                </section>

                {/* General */}
                <section>
                  <h3 className="text-lg font-semibold mb-3 text-purple-400">General</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-300">Show this help</span>
                      <div className="space-x-1">
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">?</kbd>
                        <kbd className="bg-slate-700 px-2 py-1 rounded text-xs">H</kbd>
                      </div>
                    </div>
                  </div>
                </section>

                {/* Tips */}
                <section className="bg-slate-800 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-3 text-blue-400">Tips</h3>
                  <ul className="space-y-2 text-sm text-slate-300">
                    <li>• Use number keys to quickly select cards (1 = first card, 0 = tenth card)</li>
                    <li>• Arrow keys work when you have exactly one card selected</li>
                    <li>• Press F to flip a selected card's orientation before showing</li>
                    <li>• In scouting mode, use number keys to set where the scouted card goes</li>
                    <li>• Press Esc anytime to cancel current action or clear selection</li>
                  </ul>
                </section>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
