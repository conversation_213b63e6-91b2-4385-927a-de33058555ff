import { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import { socketService } from '../services/socketService';
import { soundService } from '../services/soundService';
import { motion } from 'framer-motion';
import clsx from 'classnames';

export function ActionButtons() {
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const roundState = useGameStore(state => state.roundState);
  const selectedCards = useGameStore(state => state.selectedCards);
  const previewOrientations = useGameStore(state => state.previewOrientations);
  const scoutingMode = useGameStore(state => state.scoutingMode);
  const scoutEdge = useGameStore(state => state.scoutEdge);
  const scoutInsertIndex = useGameStore(state => state.scoutInsertIndex);
  
  const isMyTurn = useGameStore(state => state.isMyTurn);
  const canShow = useGameStore(state => state.canShow);
  const canScout = useGameStore(state => state.canScout);
  const canPass = useGameStore(state => state.canPass);
  const getSelectedCardsRange = useGameStore(state => state.getSelectedCardsRange);
  const clearSelection = useGameStore(state => state.clearSelection);
  const exitScoutingMode = useGameStore(state => state.exitScoutingMode);
  
  if (!roundState || !isMyTurn()) {
    return null;
  }
  
  const handleShow = async () => {
    const range = getSelectedCardsRange();
    if (!range) {
      setError('Please select contiguous cards');
      return;
    }
    
    setLoading('show');
    setError(null);
    
    try {
      // Build orientations array for selected cards
      const orientations: ('a' | 'b')[] = [];
      for (let i = range.start; i <= range.end; i++) {
        orientations.push(previewOrientations[i] || roundState.you.hand[i].orientation);
      }
      
      const result = await socketService.show(range.start, range.end, orientations);
      if (!result.success) {
        soundService.error();
        setError(result.error || 'Failed to show cards');
      } else {
        soundService.showCards();
        clearSelection();
      }
    } catch (error) {
      soundService.error();
      setError('Network error');
    } finally {
      setLoading(null);
    }
  };
  
  const handleScout = async () => {
    if (!scoutingMode || !scoutEdge || scoutInsertIndex === null) {
      setError('Please select scouting edge and insert position');
      return;
    }
    
    setLoading('scout');
    setError(null);
    
    try {
      // Default orientation for scouted card
      const orientation = 'a'; // Could be made configurable
      
      const result = await socketService.scout(scoutEdge, scoutInsertIndex, orientation);
      if (!result.success) {
        soundService.error();
        setError(result.error || 'Failed to scout');
      } else {
        soundService.scout();
        exitScoutingMode();
      }
    } catch (error) {
      soundService.error();
      setError('Network error');
    } finally {
      setLoading(null);
    }
  };
  
  const handlePass = async () => {
    setLoading('pass');
    setError(null);
    
    try {
      const result = await socketService.pass();
      if (!result.success) {
        soundService.error();
        setError(result.error || 'Failed to pass');
      } else {
        soundService.pass();
      }
    } catch (error) {
      soundService.error();
      setError('Network error');
    } finally {
      setLoading(null);
    }
  };
  
  const handleCompleteRound = async () => {
    setLoading('complete');
    setError(null);
    
    try {
      const success = await socketService.completeRound();
      if (!success) {
        setError('Failed to complete round');
      }
    } catch (error) {
      setError('Network error');
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="bg-white/5 rounded-xl p-4 border border-white/10" role="region" aria-label="Game actions">
      <div className="flex items-center justify-between">
        <div className="text-sm text-slate-400" role="status" aria-live="polite">
          Your turn • Choose an action
        </div>
        
        <div className="flex gap-3">
          {/* Show button */}
          <button
            className={clsx(
              "px-4 py-2 rounded-lg font-medium transition-all focus:outline-none focus:ring-2 focus:ring-brand-400",
              canShow() && !scoutingMode
                ? "bg-brand-600 hover:bg-brand-500 text-white shadow-lg"
                : "bg-slate-700 text-slate-400 cursor-not-allowed"
            )}
            onClick={handleShow}
            disabled={!canShow() || scoutingMode || loading === 'show'}
            aria-label={`Show selected cards (S or Enter)${!canShow() ? ' - disabled: select contiguous cards first' : ''}`}
          >
            {loading === 'show' ? 'Showing...' : 'Show'}
          </button>
          
          {/* Scout button */}
          {scoutingMode ? (
            <button
              className={clsx(
                "px-4 py-2 rounded-lg font-medium transition-all focus:outline-none focus:ring-2 focus:ring-amber-400",
                scoutEdge && scoutInsertIndex !== null
                  ? "bg-amber-600 hover:bg-amber-500 text-white shadow-lg"
                  : "bg-amber-700 text-amber-300 cursor-not-allowed"
              )}
              onClick={handleScout}
              disabled={!scoutEdge || scoutInsertIndex === null || loading === 'scout'}
              aria-label={`Confirm scout from ${scoutEdge} edge to position ${scoutInsertIndex !== null ? scoutInsertIndex + 1 : '?'} (Enter)`}
            >
              {loading === 'scout' ? 'Scouting...' : 'Confirm Scout'}
            </button>
          ) : (
            <div className="text-xs text-slate-500" role="status">
              Click "Scout Left/Right" on active set
            </div>
          )}
          
          {/* Pass button */}
          <button
            className={clsx(
              "px-4 py-2 rounded-lg font-medium transition-all focus:outline-none focus:ring-2 focus:ring-slate-400",
              canPass() && !scoutingMode
                ? "bg-slate-600 hover:bg-slate-500 text-white"
                : "bg-slate-700 text-slate-400 cursor-not-allowed"
            )}
            onClick={handlePass}
            disabled={!canPass() || scoutingMode || loading === 'pass'}
            aria-label={`Pass your turn (P)${!canPass() ? ' - disabled: not your turn' : ''}`}
          >
            {loading === 'pass' ? 'Passing...' : 'Pass'}
          </button>
          
          {/* Complete round button (only when round is ended) */}
          {roundState.phase === 'ended' && (
            <button
              className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg font-medium transition-all"
              onClick={handleCompleteRound}
              disabled={loading === 'complete'}
            >
              {loading === 'complete' ? 'Completing...' : 'Complete Round'}
            </button>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mt-3 p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm">
          {error}
          <button 
            className="ml-2 text-red-300 hover:text-red-200"
            onClick={() => setError(null)}
          >
            ×
          </button>
        </div>
      )}
      
      {scoutingMode && (
        <div className="mt-3 p-2 bg-amber-500/10 border border-amber-500/20 rounded text-amber-300 text-sm">
          Select insert position in your hand, then click "Confirm Scout"
          <button 
            className="ml-2 text-amber-200 hover:text-amber-100"
            onClick={exitScoutingMode}
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
}
