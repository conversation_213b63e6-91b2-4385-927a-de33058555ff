import { useGameStore } from '../store/gameStore';
import { socketService } from '../services/socketService';
import { CardVisual } from './Card';
import { PlayerHand } from './PlayerHand';
import { ActiveSetPanel } from './ActiveSetPanel';
import { ActionButtons } from './ActionButtons';
import { ScoreBoard } from './ScoreBoard';
import { DetailedScoreSheet } from './DetailedScoreSheet';
import { RoundProgression } from './RoundProgression';
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp';
import { SettingsPanel } from './SettingsPanel';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

export function GameTable() {
  const roundState = useGameStore(state => state.roundState);
  const gameState = useGameStore(state => state.gameState);
  const playerId = useGameStore(state => state.playerId);

  // Enable keyboard shortcuts
  useKeyboardShortcuts();

  if (!roundState || !gameState || !playerId) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-400 mx-auto mb-4"></div>
          <p className="text-slate-400">Loading game...</p>
        </div>
      </div>
    );
  }

  const isMyTurn = roundState.activePlayer === playerId;
  const currentPlayer = roundState.players.find(p => p.id === roundState.activePlayer);

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Header */}
      <header className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
        <h1 className="text-xl font-bold tracking-wide">
          <span className="text-brand-400">SCOUT</span> — Round {gameState.currentRound}/{gameState.totalRounds}
        </h1>
        <div className="text-sm opacity-70" role="status" aria-live="polite">
          {roundState.phase === 'playing' ? (
            isMyTurn ? "Your turn" : `${currentPlayer?.name}'s turn`
          ) : (
            "Round ended"
          )}
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left sidebar - Scores and info */}
          <div className="lg:col-span-1">
            <ScoreBoard />
          </div>

          {/* Main game area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Round progression (when round is ended) */}
            {roundState.phase === 'ended' && <RoundProgression />}

            {/* Active set panel */}
            <ActiveSetPanel />

            {/* Other players' hands */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {roundState.players
                .filter(player => player.id !== playerId)
                .map(player => (
                  <div key={player.id} className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-sm">{player.name}</h3>
                      <div className="flex items-center gap-2 text-xs text-slate-400">
                        <span>{player.handSize} cards</span>
                        {player.scoutTokens > 0 && (
                          <span className="bg-amber-500/20 text-amber-400 px-2 py-1 rounded">
                            {player.scoutTokens} 🎯
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      {Array.from({ length: Math.min(player.handSize, 8) }).map((_, i) => (
                        <div key={i} className="w-8 h-12 bg-slate-700 rounded border border-slate-600"></div>
                      ))}
                      {player.handSize > 8 && (
                        <div className="w-8 h-12 bg-slate-700 rounded border border-slate-600 flex items-center justify-center text-xs">
                          +{player.handSize - 8}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
            </div>

            {/* Action buttons */}
            <ActionButtons />

            {/* Player's hand */}
            <PlayerHand />
          </div>
        </div>
      </div>

      {/* Detailed score sheet (floating) */}
      <DetailedScoreSheet />

      {/* Keyboard shortcuts help (floating) */}
      <KeyboardShortcutsHelp />

      {/* Settings panel (floating) */}
      <SettingsPanel />
    </div>
  );
}
