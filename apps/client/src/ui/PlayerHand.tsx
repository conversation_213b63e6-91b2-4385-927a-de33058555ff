import { useGameStore } from '../store/gameStore';
import { CardVisual } from './Card';
import { soundService } from '../services/soundService';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'classnames';

export function PlayerHand() {
  const roundState = useGameStore(state => state.roundState);
  const selectedCards = useGameStore(state => state.selectedCards);
  const previewOrientations = useGameStore(state => state.previewOrientations);
  const scoutingMode = useGameStore(state => state.scoutingMode);
  const scoutInsertIndex = useGameStore(state => state.scoutInsertIndex);
  
  const selectCard = useGameStore(state => state.selectCard);
  const deselectCard = useGameStore(state => state.deselectCard);
  const toggleCardOrientation = useGameStore(state => state.toggleCardOrientation);
  const setScoutInsertIndex = useGameStore(state => state.setScoutInsertIndex);
  
  if (!roundState) return null;
  
  const hand = roundState.you.hand;
  
  const handleCardClick = (index: number) => {
    if (scoutingMode) {
      // In scouting mode, clicking sets the insert position
      setScoutInsertIndex(index);
      soundService.notification();
      return;
    }

    if (selectedCards.includes(index)) {
      deselectCard(index);
      soundService.cardDeselect();
    } else {
      selectCard(index);
      soundService.cardSelect();
    }
  };

  const handleCardDoubleClick = (index: number) => {
    if (!scoutingMode) {
      toggleCardOrientation(index);
      soundService.cardFlip();
    }
  };
  
  const getCardOrientation = (index: number) => {
    return previewOrientations[index] || hand[index].orientation;
  };
  
  const getCardValue = (cardId: string, orientation: 'a' | 'b') => {
    // Parse card ID to get values (format: "higher-lower")
    const [higher, lower] = cardId.split('-').map(Number);
    return orientation === 'a' ? higher : lower;
  };

  return (
    <div className="bg-white/5 rounded-xl p-6 border border-white/10" role="region" aria-label="Your hand">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Your Hand</h2>
        <div className="flex items-center gap-4 text-sm text-slate-400" role="status" aria-live="polite">
          {selectedCards.length > 0 && (
            <span>{selectedCards.length} selected</span>
          )}
          {scoutingMode && (
            <span className="text-amber-400">Choose insert position</span>
          )}
          <span>{hand.length} cards</span>
        </div>
      </div>
      
      <div className="relative">
        {/* Scout insert indicators */}
        {scoutingMode && (
          <div className="absolute inset-0 flex items-center pointer-events-none z-10">
            {Array.from({ length: hand.length + 1 }).map((_, i) => (
              <div
                key={i}
                className={clsx(
                  "w-2 h-20 -mx-1 rounded cursor-pointer pointer-events-auto transition-all",
                  scoutInsertIndex === i 
                    ? "bg-amber-400 shadow-lg shadow-amber-400/50" 
                    : "bg-amber-400/30 hover:bg-amber-400/50"
                )}
                onClick={() => setScoutInsertIndex(i)}
                style={{ 
                  marginLeft: i === 0 ? '0' : '70px',
                  marginRight: i === hand.length ? '0' : '70px'
                }}
              />
            ))}
          </div>
        )}
        
        {/* Cards */}
        <div className="flex gap-2 overflow-x-auto pb-4" role="group" aria-label="Your cards">
          {hand.map((card, index) => {
            const orientation = getCardOrientation(index);
            const [higher, lower] = card.cardId.split('-').map(Number);
            const isSelected = selectedCards.includes(index);
            const isContiguous = selectedCards.length > 1 && isSelected &&
              (selectedCards.includes(index - 1) || selectedCards.includes(index + 1));

            return (
              <motion.div
                key={`${card.cardId}-${index}`}
                className={clsx(
                  "relative transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-brand-400 rounded-lg",
                  scoutingMode && "opacity-50"
                )}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleCardClick(index);
                  } else if (e.key === 'f' || e.key === 'F') {
                    e.preventDefault();
                    handleCardDoubleClick(index);
                  }
                }}
                tabIndex={0}
                role="button"
                aria-pressed={isSelected}
                aria-label={`Card ${index + 1}: ${higher}-${lower}, orientation ${orientation}, ${isSelected ? 'selected' : 'not selected'}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                layout
              >
                <CardVisual
                  valueTop={higher}
                  valueBottom={lower}
                  orientation={orientation}
                  selected={isSelected}
                  onClick={() => handleCardClick(index)}
                  onDoubleClick={() => handleCardDoubleClick(index)}
                  className={clsx(
                    isContiguous && "ring-1 ring-brand-300"
                  )}
                />
                
                {/* Orientation indicator */}
                {previewOrientations[index] && previewOrientations[index] !== hand[index].orientation && (
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-brand-500 rounded-full flex items-center justify-center text-xs font-bold">
                    {orientation.toUpperCase()}
                  </div>
                )}
                
                {/* Card index for debugging */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-slate-500">
                    {index + 1}
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>
      
      {/* Selection info */}
      {selectedCards.length > 0 && !scoutingMode && (
        <div className="mt-4 p-3 bg-brand-500/10 rounded-lg border border-brand-500/20">
          <div className="text-sm text-brand-300">
            Selected: {selectedCards.map(i => `${i + 1}`).join(', ')}
            {selectedCards.length > 1 && (
              <span className="ml-2">
                ({selectedCards.length} cards)
              </span>
            )}
          </div>
          <div className="text-xs text-slate-400 mt-1">
            Double-click cards to flip orientation • Click to select/deselect
          </div>
        </div>
      )}
    </div>
  );
}
