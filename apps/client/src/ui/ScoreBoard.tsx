import { useGameStore } from '../store/gameStore';
import clsx from 'classnames';

export function ScoreBoard() {
  const gameState = useGameStore(state => state.gameState);
  const roundState = useGameStore(state => state.roundState);
  const playerId = useGameStore(state => state.playerId);
  
  if (!gameState || !roundState) return null;
  
  const isGameComplete = gameState.currentRound >= gameState.totalRounds && roundState.phase === 'ended';
  const winner = isGameComplete ? 
    Object.entries(gameState.scores).reduce((a, b) => gameState.scores[a[0]] > gameState.scores[b[0]] ? a : b)[0] : 
    null;

  return (
    <div className="space-y-4">
      {/* Game progress */}
      <div className="bg-white/5 rounded-xl p-4 border border-white/10">
        <h3 className="font-semibold mb-3">Game Progress</h3>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Round</span>
            <span>{gameState.currentRound} / {gameState.totalRounds}</span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div 
              className="bg-brand-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(gameState.currentRound / gameState.totalRounds) * 100}%` }}
            />
          </div>
          {isGameComplete && (
            <div className="text-center text-brand-400 font-medium mt-2">
              🎉 Game Complete!
            </div>
          )}
        </div>
      </div>
      
      {/* Current scores */}
      <div className="bg-white/5 rounded-xl p-4 border border-white/10">
        <h3 className="font-semibold mb-3">Scores</h3>
        <div className="space-y-2">
          {gameState.playerOrder.map(pid => {
            const player = roundState.players.find(p => p.id === pid);
            const isMe = pid === playerId;
            const isWinner = winner === pid;
            const totalScore = gameState.scores[pid] || 0;
            
            return (
              <div 
                key={pid}
                className={clsx(
                  "flex items-center justify-between p-2 rounded-lg transition-all",
                  isMe && "bg-brand-500/10 border border-brand-500/20",
                  isWinner && "bg-green-500/10 border border-green-500/20"
                )}
              >
                <div className="flex items-center gap-2">
                  <span className={clsx(
                    "font-medium",
                    isMe && "text-brand-300",
                    isWinner && "text-green-300"
                  )}>
                    {player?.name || 'Unknown'}
                  </span>
                  {isMe && <span className="text-xs text-brand-400">(You)</span>}
                  {isWinner && <span className="text-xs text-green-400">👑</span>}
                </div>
                <div className="text-right">
                  <div className={clsx(
                    "font-bold",
                    isMe && "text-brand-300",
                    isWinner && "text-green-300"
                  )}>
                    {totalScore}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Round details */}
      {roundState.phase === 'ended' && roundState.result && (
        <div className="bg-white/5 rounded-xl p-4 border border-white/10">
          <h3 className="font-semibold mb-3">Round {gameState.currentRound} Results</h3>
          <div className="space-y-2 text-sm">
            <div className="text-slate-400 mb-2">
              Ended by: {roundState.result.reason === 'empty_hand' ? 'Empty hand' : 'All passed'}
              {roundState.result.winner && (
                <span className="ml-1">
                  ({roundState.players.find(p => p.id === roundState.result?.winner)?.name})
                </span>
              )}
            </div>
            
            {gameState.playerOrder.map(pid => {
              const player = roundState.players.find(p => p.id === pid);
              const roundScore = roundState.result?.scores[pid] || 0;
              const isMe = pid === playerId;
              
              return (
                <div 
                  key={pid}
                  className={clsx(
                    "flex justify-between p-2 rounded",
                    isMe && "bg-brand-500/10"
                  )}
                >
                  <span className={isMe ? "text-brand-300" : "text-slate-300"}>
                    {player?.name}
                  </span>
                  <span className={clsx(
                    "font-mono",
                    roundScore > 0 ? "text-green-400" : roundScore < 0 ? "text-red-400" : "text-slate-400"
                  )}>
                    {roundScore > 0 ? '+' : ''}{roundScore}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Player stats */}
      <div className="bg-white/5 rounded-xl p-4 border border-white/10">
        <h3 className="font-semibold mb-3">Current Round</h3>
        <div className="space-y-2 text-sm">
          {roundState.players.map(player => {
            const isMe = player.id === playerId;
            const isActive = player.id === roundState.activePlayer;
            
            return (
              <div 
                key={player.id}
                className={clsx(
                  "flex items-center justify-between p-2 rounded-lg",
                  isMe && "bg-brand-500/10",
                  isActive && "ring-1 ring-brand-400"
                )}
              >
                <div className="flex items-center gap-2">
                  <span className={clsx(
                    "font-medium",
                    isMe && "text-brand-300",
                    isActive && "text-brand-400"
                  )}>
                    {player.name}
                  </span>
                  {isActive && <span className="text-xs text-brand-400">⭐</span>}
                </div>
                <div className="flex items-center gap-3 text-xs text-slate-400">
                  <span>{player.handSize} 🃏</span>
                  <span>{player.facedownCount} 📚</span>
                  {player.scoutTokens > 0 && (
                    <span className="text-amber-400">{player.scoutTokens} 🎯</span>
                  )}
                  {player.usedScoutShow && (
                    <span className="text-purple-400">⚡</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-3 pt-3 border-t border-white/10 text-xs text-slate-500">
          🃏 Cards in hand • 📚 Facedown cards • 🎯 Scout tokens • ⚡ Used Scout&Show
        </div>
      </div>
    </div>
  );
}
