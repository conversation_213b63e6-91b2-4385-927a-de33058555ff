import { useState } from 'react';
import { useGameStore } from '../store/gameStore';
import clsx from 'classnames';

export function DetailedScoreSheet() {
  const [isOpen, setIsOpen] = useState(false);
  const gameState = useGameStore(state => state.gameState);
  const roundState = useGameStore(state => state.roundState);
  const playerId = useGameStore(state => state.playerId);
  
  if (!gameState || !roundState) return null;
  
  const isGameComplete = gameState.currentRound >= gameState.totalRounds && roundState.phase === 'ended';
  const sortedPlayers = [...gameState.playerOrder].sort((a, b) => 
    (gameState.scores[b] || 0) - (gameState.scores[a] || 0)
  );

  return (
    <>
      {/* Toggle button */}
      <button
        className="fixed bottom-4 right-4 bg-brand-600 hover:bg-brand-500 text-white p-3 rounded-full shadow-lg transition-all z-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        📊
      </button>
      
      {/* Score sheet modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-slate-900 rounded-xl border border-white/10 max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold">Score Sheet</h2>
              <button
                className="text-slate-400 hover:text-white"
                onClick={() => setIsOpen(false)}
              >
                ✕
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {/* Game summary */}
              <div className="mb-6 p-4 bg-white/5 rounded-lg">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-brand-400">{gameState.currentRound}</div>
                    <div className="text-sm text-slate-400">Current Round</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-slate-300">{gameState.totalRounds}</div>
                    <div className="text-sm text-slate-400">Total Rounds</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-400">
                      {Math.max(...Object.values(gameState.scores))}
                    </div>
                    <div className="text-sm text-slate-400">Highest Score</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-slate-300">{gameState.playerOrder.length}</div>
                    <div className="text-sm text-slate-400">Players</div>
                  </div>
                </div>
              </div>
              
              {/* Detailed score table */}
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-left p-3 font-semibold">Player</th>
                      {Array.from({ length: gameState.totalRounds }, (_, i) => (
                        <th key={i} className="text-center p-3 font-semibold min-w-[60px]">
                          R{i + 1}
                        </th>
                      ))}
                      <th className="text-center p-3 font-semibold min-w-[80px]">Total</th>
                      <th className="text-center p-3 font-semibold min-w-[60px]">Rank</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedPlayers.map((pid, rank) => {
                      const player = roundState.players.find(p => p.id === pid);
                      const isMe = pid === playerId;
                      const totalScore = gameState.scores[pid] || 0;
                      
                      return (
                        <tr 
                          key={pid}
                          className={clsx(
                            "border-b border-white/5",
                            isMe && "bg-brand-500/10",
                            rank === 0 && isGameComplete && "bg-green-500/10"
                          )}
                        >
                          <td className="p-3">
                            <div className="flex items-center gap-2">
                              <span className={clsx(
                                "font-medium",
                                isMe && "text-brand-300",
                                rank === 0 && isGameComplete && "text-green-300"
                              )}>
                                {player?.name || 'Unknown'}
                              </span>
                              {isMe && <span className="text-xs text-brand-400">(You)</span>}
                              {rank === 0 && isGameComplete && <span className="text-xs">👑</span>}
                            </div>
                          </td>
                          
                          {/* Round scores */}
                          {Array.from({ length: gameState.totalRounds }, (_, roundIndex) => {
                            const roundScore = gameState.roundScores[roundIndex]?.[pid];
                            const isCurrentRound = roundIndex + 1 === gameState.currentRound;
                            
                            return (
                              <td key={roundIndex} className="text-center p-3">
                                {roundScore !== undefined ? (
                                  <span className={clsx(
                                    "font-mono",
                                    roundScore > 0 ? "text-green-400" : 
                                    roundScore < 0 ? "text-red-400" : "text-slate-400",
                                    isCurrentRound && "font-bold"
                                  )}>
                                    {roundScore > 0 ? '+' : ''}{roundScore}
                                  </span>
                                ) : isCurrentRound && roundState.phase === 'ended' && roundState.result ? (
                                  <span className={clsx(
                                    "font-mono font-bold",
                                    (roundState.result.scores[pid] || 0) > 0 ? "text-green-400" : 
                                    (roundState.result.scores[pid] || 0) < 0 ? "text-red-400" : "text-slate-400"
                                  )}>
                                    {(roundState.result.scores[pid] || 0) > 0 ? '+' : ''}{roundState.result.scores[pid] || 0}
                                  </span>
                                ) : (
                                  <span className="text-slate-600">—</span>
                                )}
                              </td>
                            );
                          })}
                          
                          {/* Total score */}
                          <td className="text-center p-3">
                            <span className={clsx(
                              "font-bold text-lg",
                              isMe && "text-brand-300",
                              rank === 0 && isGameComplete && "text-green-300"
                            )}>
                              {totalScore}
                            </span>
                          </td>
                          
                          {/* Rank */}
                          <td className="text-center p-3">
                            <span className={clsx(
                              "font-semibold",
                              rank === 0 && "text-green-400",
                              rank === 1 && "text-yellow-400",
                              rank === 2 && "text-orange-400"
                            )}>
                              {rank + 1}
                              {rank === 0 && isGameComplete && '🥇'}
                              {rank === 1 && isGameComplete && '🥈'}
                              {rank === 2 && isGameComplete && '🥉'}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              
              {/* Game completion message */}
              {isGameComplete && (
                <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                  <div className="text-xl font-bold text-green-300 mb-2">
                    🎉 Game Complete! 🎉
                  </div>
                  <div className="text-green-400">
                    Winner: {roundState.players.find(p => p.id === sortedPlayers[0])?.name}
                  </div>
                  <div className="text-sm text-slate-400 mt-2">
                    Final Score: {gameState.scores[sortedPlayers[0]]} points
                  </div>
                </div>
              )}
              
              {/* Scoring rules reminder */}
              <div className="mt-6 p-4 bg-slate-800 rounded-lg">
                <h3 className="font-semibold mb-2">Scoring Rules</h3>
                <div className="text-sm text-slate-400 space-y-1">
                  <div>• +1 point per facedown card collected</div>
                  <div>• +1 point per Scout token held</div>
                  <div>• -1 point per card remaining in hand</div>
                  <div>• Exception: Player whose set ended the round suffers no hand penalty</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
