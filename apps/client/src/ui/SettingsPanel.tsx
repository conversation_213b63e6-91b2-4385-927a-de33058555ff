import React, { useState } from 'react';
import { soundService } from '../services/soundService';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'classnames';

export function SettingsPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [soundVolume, setSoundVolume] = useState(0.3);
  const [useProductionSounds, setUseProductionSounds] = useState(true);
  const [animationsEnabled, setAnimationsEnabled] = useState(true);
  const [soundStats, setSoundStats] = useState<any>(null);

  const handleSoundToggle = (enabled: boolean) => {
    setSoundEnabled(enabled);
    soundService.setEnabled(enabled);
    if (enabled) {
      soundService.notification();
    }
  };

  const handleVolumeChange = (volume: number) => {
    setSoundVolume(volume);
    soundService.setVolume(volume);
    soundService.notification();
  };

  const handleProductionSoundsToggle = (useProduction: boolean) => {
    setUseProductionSounds(useProduction);
    soundService.setUseProductionSounds(useProduction);
    soundService.notification();
    updateSoundStats();
  };

  const updateSoundStats = () => {
    setSoundStats(soundService.getCacheStats());
  };

  const preloadSounds = async () => {
    try {
      await soundService.preloadSounds();
      updateSoundStats();
      soundService.notification();
    } catch (error) {
      console.error('Failed to preload sounds:', error);
      soundService.error();
    }
  };

  const clearSoundCache = () => {
    soundService.clearCache();
    updateSoundStats();
    soundService.notification();
  };

  const testSound = (soundType: keyof typeof soundService) => {
    if (typeof soundService[soundType] === 'function') {
      (soundService[soundType] as Function)();
    }
  };

  // Update sound stats when panel opens
  React.useEffect(() => {
    if (isOpen) {
      updateSoundStats();
    }
  }, [isOpen]);

  return (
    <>
      {/* Settings button */}
      <button
        className="fixed top-4 right-4 bg-slate-700 hover:bg-slate-600 text-white p-3 rounded-full shadow-lg transition-all z-50"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Open settings"
        title="Settings"
      >
        ⚙️
      </button>
      
      {/* Settings modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div 
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            role="dialog"
            aria-modal="true"
            aria-labelledby="settings-title"
          >
            <motion.div 
              className="bg-slate-900 rounded-xl border border-white/10 max-w-md w-full"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <h2 id="settings-title" className="text-xl font-bold">Settings</h2>
                <button
                  className="text-slate-400 hover:text-white transition-colors"
                  onClick={() => setIsOpen(false)}
                  aria-label="Close settings"
                >
                  ✕
                </button>
              </div>
              
              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Sound Settings */}
                <section>
                  <h3 className="text-lg font-semibold mb-4 text-circus-orange">Sound Effects</h3>
                  
                  {/* Sound Toggle */}
                  <div className="flex items-center justify-between mb-4">
                    <label htmlFor="sound-toggle" className="text-slate-300">
                      Enable Sound Effects
                    </label>
                    <button
                      id="sound-toggle"
                      className={clsx(
                        "relative w-12 h-6 rounded-full transition-colors",
                        soundEnabled ? "bg-circus-green" : "bg-slate-600"
                      )}
                      onClick={() => handleSoundToggle(!soundEnabled)}
                      role="switch"
                      aria-checked={soundEnabled}
                    >
                      <div
                        className={clsx(
                          "absolute top-1 w-4 h-4 bg-white rounded-full transition-transform",
                          soundEnabled ? "translate-x-7" : "translate-x-1"
                        )}
                      />
                    </button>
                  </div>
                  
                  {/* Production Sounds Toggle */}
                  {soundEnabled && (
                    <div className="flex items-center justify-between">
                      <label htmlFor="production-sounds-toggle" className="text-slate-300">
                        Use Production Sounds
                      </label>
                      <button
                        id="production-sounds-toggle"
                        className={clsx(
                          "relative w-12 h-6 rounded-full transition-colors",
                          useProductionSounds ? "bg-circus-green" : "bg-slate-600"
                        )}
                        onClick={() => handleProductionSoundsToggle(!useProductionSounds)}
                        role="switch"
                        aria-checked={useProductionSounds}
                      >
                        <div
                          className={clsx(
                            "absolute top-1 w-4 h-4 bg-white rounded-full transition-transform",
                            useProductionSounds ? "translate-x-7" : "translate-x-1"
                          )}
                        />
                      </button>
                    </div>
                  )}

                  {/* Volume Slider */}
                  {soundEnabled && (
                    <div className="space-y-2">
                      <label htmlFor="volume-slider" className="text-slate-300 text-sm">
                        Volume: {Math.round(soundVolume * 100)}%
                      </label>
                      <input
                        id="volume-slider"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={soundVolume}
                        onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                        className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, #4ecdc4 0%, #4ecdc4 ${soundVolume * 100}%, #374151 ${soundVolume * 100}%, #374151 100%)`
                        }}
                      />
                    </div>
                  )}

                  {/* Sound Management */}
                  {soundEnabled && useProductionSounds && (
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <button
                          className="px-3 py-1 bg-circus-green hover:bg-circus-green/80 text-white rounded text-sm transition-colors"
                          onClick={preloadSounds}
                        >
                          Preload Sounds
                        </button>
                        <button
                          className="px-3 py-1 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
                          onClick={clearSoundCache}
                        >
                          Clear Cache
                        </button>
                      </div>

                      {/* Sound Stats */}
                      {soundStats && (
                        <div className="text-xs text-slate-400 space-y-1">
                          <div>Cached: {soundStats.cachedSounds} sounds</div>
                          <div>Assets: {soundStats.totalAssets} | Events: {soundStats.totalEvents}</div>
                          <div>Version: {soundStats.manifestVersion}</div>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Sound Test Buttons */}
                  {soundEnabled && (
                    <div className="grid grid-cols-2 gap-2 mt-4">
                      <button
                        className="px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
                        onClick={() => testSound('cardSelect')}
                      >
                        Card Select
                      </button>
                      <button
                        className="px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
                        onClick={() => testSound('showCards')}
                      >
                        Show Cards
                      </button>
                      <button
                        className="px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
                        onClick={() => testSound('scout')}
                      >
                        Scout
                      </button>
                      <button
                        className="px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded text-sm transition-colors"
                        onClick={() => testSound('gameWin')}
                      >
                        Victory
                      </button>
                    </div>
                  )}
                </section>

                {/* Visual Settings */}
                <section>
                  <h3 className="text-lg font-semibold mb-4 text-circus-blue">Visual Effects</h3>
                  
                  {/* Animations Toggle */}
                  <div className="flex items-center justify-between mb-4">
                    <label htmlFor="animations-toggle" className="text-slate-300">
                      Enable Animations
                    </label>
                    <button
                      id="animations-toggle"
                      className={clsx(
                        "relative w-12 h-6 rounded-full transition-colors",
                        animationsEnabled ? "bg-circus-blue" : "bg-slate-600"
                      )}
                      onClick={() => setAnimationsEnabled(!animationsEnabled)}
                      role="switch"
                      aria-checked={animationsEnabled}
                    >
                      <div
                        className={clsx(
                          "absolute top-1 w-4 h-4 bg-white rounded-full transition-transform",
                          animationsEnabled ? "translate-x-7" : "translate-x-1"
                        )}
                      />
                    </button>
                  </div>
                  
                  <p className="text-sm text-slate-400">
                    Controls card animations, transitions, and visual effects throughout the game.
                  </p>
                </section>

                {/* Game Info */}
                <section className="pt-4 border-t border-white/10">
                  <h3 className="text-lg font-semibold mb-2 text-circus-purple">About</h3>
                  <div className="text-sm text-slate-400 space-y-1">
                    <p>Scout Card Game</p>
                    <p>Built with React, TypeScript, and Framer Motion</p>
                    <p>Sound effects generated procedurally</p>
                  </div>
                </section>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

    </>
  );
}
