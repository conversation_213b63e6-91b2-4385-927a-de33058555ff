import { useEffect } from 'react';
import { useGameStore } from '../store/gameStore';
import { socketService } from '../services/socketService';

export function useKeyboardShortcuts() {
  const roundState = useGameStore(state => state.roundState);
  const selectedCards = useGameStore(state => state.selectedCards);
  const previewOrientations = useGameStore(state => state.previewOrientations);
  const scoutingMode = useGameStore(state => state.scoutingMode);
  const scoutEdge = useGameStore(state => state.scoutEdge);
  const scoutInsertIndex = useGameStore(state => state.scoutInsertIndex);
  
  const isMyTurn = useGameStore(state => state.isMyTurn);
  const canShow = useGameStore(state => state.canShow);
  const canScout = useGameStore(state => state.canScout);
  const canPass = useGameStore(state => state.canPass);
  const getSelectedCardsRange = useGameStore(state => state.getSelectedCardsRange);
  
  const selectCard = useGameStore(state => state.selectCard);
  const deselectCard = useGameStore(state => state.deselectCard);
  const clearSelection = useGameStore(state => state.clearSelection);
  const toggleCardOrientation = useGameStore(state => state.toggleCardOrientation);
  const enterScoutingMode = useGameStore(state => state.enterScoutingMode);
  const exitScoutingMode = useGameStore(state => state.exitScoutingMode);
  const setScoutInsertIndex = useGameStore(state => state.setScoutInsertIndex);

  useEffect(() => {
    const handleKeyDown = async (event: KeyboardEvent) => {
      // Don't handle shortcuts if user is typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }
      
      // Don't handle shortcuts if it's not the player's turn (except for UI shortcuts)
      const isUIShortcut = ['Escape', 'Tab', '?', 'h'].includes(event.key);
      if (!isMyTurn() && !isUIShortcut) {
        return;
      }
      
      // Prevent default for handled shortcuts
      const handledKeys = [
        'Enter', 'Space', 'Escape', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
        's', 'p', 'c', 'f', 'l', 'r', 'q', 'w', 'e', '?', 'h'
      ];
      
      if (handledKeys.includes(event.key)) {
        event.preventDefault();
      }
      
      // Handle shortcuts based on current mode
      if (scoutingMode) {
        await handleScoutingModeShortcuts(event);
      } else {
        await handleNormalModeShortcuts(event);
      }
    };

    const handleNormalModeShortcuts = async (event: KeyboardEvent) => {
      switch (event.key) {
        // Card selection (1-9, 0 for 10th card)
        case '1': case '2': case '3': case '4': case '5':
        case '6': case '7': case '8': case '9': case '0':
          if (roundState?.you.hand) {
            const index = event.key === '0' ? 9 : parseInt(event.key) - 1;
            if (index < roundState.you.hand.length) {
              if (selectedCards.includes(index)) {
                deselectCard(index);
              } else {
                selectCard(index);
              }
            }
          }
          break;
          
        // Navigation through hand
        case 'ArrowLeft':
          if (roundState?.you.hand && selectedCards.length === 1) {
            const currentIndex = selectedCards[0];
            if (currentIndex > 0) {
              deselectCard(currentIndex);
              selectCard(currentIndex - 1);
            }
          }
          break;
          
        case 'ArrowRight':
          if (roundState?.you.hand && selectedCards.length === 1) {
            const currentIndex = selectedCards[0];
            if (currentIndex < roundState.you.hand.length - 1) {
              deselectCard(currentIndex);
              selectCard(currentIndex + 1);
            }
          }
          break;
          
        // Flip card orientation
        case 'f':
          if (selectedCards.length === 1) {
            toggleCardOrientation(selectedCards[0]);
          }
          break;
          
        // Actions
        case 'Enter':
        case 's':
          if (canShow()) {
            const range = getSelectedCardsRange();
            if (range) {
              const orientations: ('a' | 'b')[] = [];
              for (let i = range.start; i <= range.end; i++) {
                orientations.push(previewOrientations[i] || roundState!.you.hand[i].orientation);
              }
              await socketService.show(range.start, range.end, orientations);
              clearSelection();
            }
          }
          break;
          
        case 'p':
          if (canPass()) {
            await socketService.pass();
          }
          break;
          
        case 'l':
          if (canScout()) {
            enterScoutingMode('left');
          }
          break;
          
        case 'r':
          if (canScout()) {
            enterScoutingMode('right');
          }
          break;
          
        // Clear selection
        case 'Escape':
        case 'c':
          clearSelection();
          break;
          
        // Select all cards
        case 'a':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            if (roundState?.you.hand) {
              for (let i = 0; i < roundState.you.hand.length; i++) {
                selectCard(i);
              }
            }
          }
          break;
      }
    };

    const handleScoutingModeShortcuts = async (event: KeyboardEvent) => {
      switch (event.key) {
        // Set insert position (1-9, 0 for 10th position)
        case '1': case '2': case '3': case '4': case '5':
        case '6': case '7': case '8': case '9': case '0':
          if (roundState?.you.hand) {
            const index = event.key === '0' ? 9 : parseInt(event.key) - 1;
            if (index <= roundState.you.hand.length) {
              setScoutInsertIndex(index);
            }
          }
          break;
          
        // Navigate insert position
        case 'ArrowLeft':
          if (scoutInsertIndex !== null && scoutInsertIndex > 0) {
            setScoutInsertIndex(scoutInsertIndex - 1);
          }
          break;
          
        case 'ArrowRight':
          if (roundState?.you.hand && scoutInsertIndex !== null && scoutInsertIndex < roundState.you.hand.length) {
            setScoutInsertIndex(scoutInsertIndex + 1);
          }
          break;
          
        // Confirm scout
        case 'Enter':
          if (scoutEdge && scoutInsertIndex !== null) {
            await socketService.scout(scoutEdge, scoutInsertIndex, 'a');
            exitScoutingMode();
          }
          break;
          
        // Cancel scouting
        case 'Escape':
          exitScoutingMode();
          break;
      }
    };

    // Show help
    const showHelp = () => {
      const helpText = `
Keyboard Shortcuts:

Card Selection:
• 1-9, 0: Select/deselect cards (0 = 10th card)
• ← →: Navigate through hand (when one card selected)
• Ctrl/Cmd + A: Select all cards
• F: Flip orientation of selected card
• C or Esc: Clear selection

Actions:
• S or Enter: Show selected cards
• P: Pass turn
• L: Scout left edge
• R: Scout right edge

Scouting Mode:
• 1-9, 0: Set insert position
• ← →: Navigate insert positions
• Enter: Confirm scout
• Esc: Cancel scouting

General:
• ? or H: Show this help
      `.trim();
      
      alert(helpText);
    };

    const handleHelpShortcut = (event: KeyboardEvent) => {
      if (event.key === '?' || event.key === 'h') {
        event.preventDefault();
        showHelp();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keydown', handleHelpShortcut);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keydown', handleHelpShortcut);
    };
  }, [
    roundState, selectedCards, previewOrientations, scoutingMode, scoutEdge, scoutInsertIndex,
    isMyTurn, canShow, canScout, canPass, getSelectedCardsRange,
    selectCard, deselectCard, clearSelection, toggleCardOrientation,
    enterScoutingMode, exitScoutingMode, setScoutInsertIndex
  ]);
}
