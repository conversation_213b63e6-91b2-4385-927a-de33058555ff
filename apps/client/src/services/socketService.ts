import io from 'socket.io-client';
import type { 
  ClientToServerEvents, 
  ServerToClientEvents, 
  RoomId, 
  Orientation 
} from '@scout/protocol';
import { useGameStore } from '../store/gameStore';

class SocketService {
  private socket: import('socket.io-client').Socket<ServerToClientEvents, ClientToServerEvents>;
  
  constructor() {
    this.socket = io('http://localhost:4000', {
      autoConnect: true,
      transports: ['websocket']
    });
    
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    this.socket.on('connect', () => {
      console.log('Connected to server');
      useGameStore.getState().setConnected(true);
    });
    
    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      useGameStore.getState().setConnected(false);
    });
    
    this.socket.on('roomState', (gameState) => {
      console.log('Received room state:', gameState);
      useGameStore.getState().setGameState(gameState);
    });
    
    this.socket.on('roundState', (roundState) => {
      console.log('Received round state:', roundState);
      useGameStore.getState().setRoundState(roundState);
    });
    
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }
  
  // Room management
  createRoom(name: string): Promise<{ roomId: RoomId; playerId: string; token: string }> {
    return new Promise((resolve, reject) => {
      this.socket.emit('createRoom', { name }, (response) => {
        if (response) {
          useGameStore.getState().setRoomId(response.roomId);
          useGameStore.getState().setPlayerId(response.playerId);
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
  }
  
  joinRoom(roomId: RoomId, name: string): Promise<{ playerId: string; token: string }> {
    return new Promise((resolve, reject) => {
      this.socket.emit('joinRoom', { roomId, name }, (response) => {
        if (response.ok) {
          useGameStore.getState().setRoomId(roomId);
          useGameStore.getState().setPlayerId(response.playerId);
          resolve({ playerId: response.playerId, token: response.token });
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }
  
  // Game actions
  startRound(playerCount: number, variant?: 'standard' | 'two-player'): Promise<boolean> {
    return new Promise((resolve) => {
      this.socket.emit('startRound', { playerCount, variant }, (success) => {
        resolve(success);
      });
    });
  }
  
  chooseInitialHandOrientation(orientation: Orientation): Promise<boolean> {
    return new Promise((resolve) => {
      this.socket.emit('chooseInitialHandOrientation', { orientation }, (success) => {
        resolve(success);
      });
    });
  }
  
  show(start: number, endInclusive: number, orientations?: Orientation[]): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      this.socket.emit('show', { start, endInclusive, orientations }, (success, error) => {
        resolve({ success, error });
      });
    });
  }
  
  scout(edge: 'left' | 'right', insertAt: number, orientation: Orientation, useScoutAndShow?: boolean): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      this.socket.emit('scout', { edge, insertAt, orientation, useScoutAndShow }, (success, error) => {
        resolve({ success, error });
      });
    });
  }
  
  pass(): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      this.socket.emit('pass', {}, (success, error) => {
        resolve({ success, error });
      });
    });
  }
  
  completeRound(): Promise<boolean> {
    return new Promise((resolve) => {
      this.socket.emit('completeRound', {}, (success) => {
        resolve(success);
      });
    });
  }
  
  requestState(): Promise<any> {
    return new Promise((resolve) => {
      this.socket.emit('requestState', {}, (state) => {
        resolve(state);
      });
    });
  }
  
  // Utility methods
  isConnected(): boolean {
    return this.socket.connected;
  }
  
  disconnect() {
    this.socket.disconnect();
  }
}

// Export singleton instance
export const socketService = new SocketService();
