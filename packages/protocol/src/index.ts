export type PlayerId = string;
export type RoomId = string;

export type Orientation = 'a' | 'b';

export interface Card {
  id: string; // stable id "i-j" where i>j
  a: number; // higher value by convention
  b: number; // lower value by convention
}

export interface CardInHand {
  cardId: string;
  orientation: Orientation; // which value is active for this card in hand
}

export interface PlayedCard {
  cardId: string;
  value: number; // resolved value at time of play
}

export interface PlayedSet {
  ownerId: PlayerId;
  type: 'kind' | 'run';
  cards: PlayedCard[]; // in order as played
  length: number; // cards.length
  minValue: number; // for tie-breakers
}

export interface PlayerPublic {
  id: PlayerId;
  name: string;
  handSize: number;
  facedownCount: number;
  scoutTokens: number;
  usedScoutShow: boolean;
}

export interface PlayerPrivate {
  id: PlayerId;
  hand: CardInHand[];
}

export type Phase = 'lobby' | 'playing' | 'ended';

export interface RoundResult {
  scores: Record<PlayerId, number>;
  winner?: PlayerId; // player who emptied their hand, if any
  reason: 'empty_hand' | 'all_passed';
}

export interface RoundStatePublic {
  phase: Phase;
  activePlayer: PlayerId;
  activeSet?: PlayedSet;
  players: PlayerPublic[];
  unbeatenSince?: PlayerId; // last player who showed a set that remains unbeaten
  playerOrder: PlayerId[];
  result?: RoundResult; // present when phase is 'ended'
}

export interface RoundStateForPlayer extends RoundStatePublic {
  you: PlayerPrivate;
}

export interface GameScores {
  [playerId: string]: number;
}

export interface GameStatePublic {
  roomId: RoomId;
  playerOrder: PlayerId[];
  scores: GameScores; // cumulative scores
  roundScores: GameScores[]; // scores for each completed round
  currentRound: number;
  totalRounds: number;
  round: RoundStatePublic | null;
}

// Socket events
export type ClientToServerEvents = {
  createRoom: (payload: { name: string }, cb: (resp: { roomId: RoomId; playerId: PlayerId; token: string }) => void) => void;
  joinRoom: (payload: { roomId: RoomId; name: string }, cb: (resp: { ok: true; playerId: PlayerId; token: string } | { ok: false; error: string }) => void) => void;
  startRound: (payload: { playerCount: number; variant?: 'standard' | 'two-player' }, cb: (ok: boolean) => void) => void;
  chooseInitialHandOrientation: (payload: { orientation: 'a' | 'b' }, cb: (ok: boolean) => void) => void;
  show: (payload: { start: number; endInclusive: number; orientations?: Orientation[] }, cb: (ok: boolean, error?: string) => void) => void;
  scout: (payload: { edge: 'left' | 'right'; insertAt: number; orientation: Orientation; useScoutAndShow?: boolean }, cb: (ok: boolean, error?: string) => void) => void;
  pass: (payload: {}, cb: (ok: boolean, error?: string) => void) => void;
  completeRound: (payload: {}, cb: (ok: boolean) => void) => void;
  requestState: (payload: {}, cb: (state: RoundStateForPlayer | null) => void) => void;
};

export type ServerToClientEvents = {
  roomState: (payload: GameStatePublic) => void;
  roundState: (payload: RoundStateForPlayer) => void;
  error: (payload: { message: string }) => void;
};

