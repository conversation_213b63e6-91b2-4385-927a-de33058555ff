import type { Card, CardInHand, Orientation, PlayedCard, PlayedSet } from '@scout/protocol';

export function cardValue(card: Card, orientation: Orientation): number {
  return orientation === 'a' ? card.a : card.b;
}

export function isContiguousRange(start: number, endInclusive: number, hand: CardInHand[]): boolean {
  return (
    Number.isInteger(start) &&
    Number.isInteger(endInclusive) &&
    start >= 0 &&
    endInclusive < hand.length &&
    start <= endInclusive
  );
}

export function sliceToPlayed(cards: CardInHand[], start: number, endInclusive: number, deck: Map<string, Card>, orientations?: Orientation[]): PlayedCard[] {
  const out: PlayedCard[] = [];
  for (let i = start; i <= endInclusive; i++) {
    const inHand = cards[i];
    const card = deck.get(inHand.cardId);
    if (!card) throw new Error('Card not in deck');
    const o: Orientation = orientations?.[i - start] ?? inHand.orientation;
    out.push({ cardId: inHand.cardId, value: cardValue(card, o) });
  }
  return out;
}

export function classifySet(played: PlayedCard[]): { type: 'kind' | 'run' | 'invalid'; length: number; minValue: number } {
  const values = played.map((c) => c.value);
  const length = values.length;
  if (length === 0) return { type: 'invalid', length, minValue: 0 };

  const allSame = values.every((v) => v === values[0]);
  if (allSame) return { type: 'kind', length, minValue: values[0] };

  // Check for run, ascending or descending, strictly consecutive
  const asc = [...values].every((v, i) => i === 0 || v - values[i - 1] === 1);
  const desc = [...values].every((v, i) => i === 0 || values[i - 1] - v === 1);
  if (asc || desc) return { type: 'run', length, minValue: Math.min(...values) };

  return { type: 'invalid', length, minValue: 0 };
}

export function beats(candidate: PlayedSet, active?: PlayedSet): boolean {
  if (!active) return true; // empty table — anything goes
  if (candidate.length !== candidate.cards.length) throw new Error('Bad candidate length');

  if (candidate.length > active.length) return true;
  if (candidate.length < active.length) return false;
  // Same length
  if (candidate.type === 'kind' && active.type === 'run') return true;
  if (candidate.type === 'run' && active.type === 'kind') return false;

  if (candidate.type !== active.type) return false; // identical set type required for tiebreak

  if (candidate.minValue > active.minValue) return true;
  if (candidate.minValue < active.minValue) return false;
  // Identical type, length, and min value — treated as a match; rules: cannot place identical set
  return false;
}

