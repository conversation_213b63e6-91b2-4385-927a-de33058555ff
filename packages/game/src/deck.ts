import type { Card } from '@scout/protocol';

// Build the canonical Scout deck as all unordered pairs (i,j) with 10 >= i > j >= 1.
export function buildCanonicalDeck(): Card[] {
  const deck: Card[] = [];
  for (let i = 10; i >= 2; i--) {
    for (let j = i - 1; j >= 1; j--) {
      deck.push({ id: `${i}-${j}`, a: i, b: j });
    }
  }
  return deck;
}

export type PlayerCount = 2 | 3 | 4 | 5;

export function filterDeckForPlayerCount(deck: Card[], playerCount: PlayerCount): Card[] {
  if (playerCount === 3) {
    // remove all cards that contain a 10
    return deck.filter((c) => !(c.a === 10 || c.b === 10));
  }
  if (playerCount === 4) {
    // remove the single 10-9 card
    return deck.filter((c) => c.id !== '10-9');
  }
  if (playerCount === 2) {
    // Official 2-player variant: remove the single 10-9 card (same as 4-player)
    return deck.filter((c) => c.id !== '10-9');
  }
  // 5p uses all cards
  return deck.slice();
}

export function dealCounts(playerCount: PlayerCount): number {
  switch (playerCount) {
    case 3:
      return 12;
    case 4:
      return 11;
    case 5:
      return 9;
    case 2:
      // Official 2-player variant: 11 cards each
      return 11;
  }
}

