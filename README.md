# SCOUT (Web)

React + Node + WebSockets implementation of the board game SCOUT.

- Client: `apps/client` (React + Vite + Tailwind)
- Server: `apps/server` (Express + Socket.IO)
- Game engine: `packages/game` (pure TypeScript)
- Protocol types: `packages/protocol`
- Docs: `docs/` (context, rules, plan)

Dev quickstart:

1. Install deps in workspaces (Node 20+): `npm install` (from repo root)
2. Run both apps: `npm run dev`
3. Server: http://localhost:4000 (Socket.IO). Client: http://localhost:5173

See `docs/CONTEXT.md` and `docs/rules.md` for the consolidated rules and open questions.

