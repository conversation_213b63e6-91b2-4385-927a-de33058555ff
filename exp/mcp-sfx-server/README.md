# MCP SFX Server

A Model Context Protocol (MCP) server for boardgame sound effects search, download, and normalization.

## Features

- **Multi-Provider Search**: Search across Kenney, Freesound, Sonniss, and OpenGameArt
- **Audio Normalization**: 48kHz, -3dBFS peak, mono/stereo conversion
- **License Tracking**: Proper attribution and audit trails
- **Local Caching**: Efficient storage in `~/.game-sfx-cache`
- **Format Support**: WAV and OGG output formats

## Installation

```bash
npm install
```

## Usage

### Development

```bash
npm run dev
```

### Production

```bash
npm run build
npm start
```

### Testing

```bash
# Test with echo
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}' | npm run dev
```

## MCP Tools

### `search_sfx`

Search for sound effects across multiple providers.

**Parameters:**
- `q` (string): Search query
- `license` (array, optional): License filter ["CC0", "CC-BY", "ROYALTY_FREE"]
- `limit` (number, optional): Maximum results (default: 10)

**Example:**
```json
{
  "name": "search_sfx",
  "arguments": {
    "q": "card flip paper",
    "license": ["CC0"],
    "limit": 5
  }
}
```

### `download_sfx`

Download and normalize a specific sound effect.

**Parameters:**
- `id` (string): SFX ID from search results
- `source` (string): Provider ("kenney", "freesound", "sonniss", "opengameart")
- `format` (string, optional): Output format ("wav", "ogg")
- `mono` (boolean, optional): Convert to mono

**Example:**
```json
{
  "name": "download_sfx",
  "arguments": {
    "id": "kenney_ui_click_001",
    "source": "kenney",
    "format": "ogg",
    "mono": true
  }
}
```

### `normalize_sfx`

Normalize an existing audio file.

**Parameters:**
- `inputPath` (string): Path to input file
- `outputPath` (string, optional): Output path
- `format` (string, optional): Output format
- `mono` (boolean, optional): Convert to mono
- `peakDb` (number, optional): Peak level in dB (default: -3)

### `catalog_sfx`

Get recommended SFX categories for boardgames.

**Returns:** Categorized list of recommended sound types.

## Configuration

### Environment Variables

- `FREESOUND_API_KEY`: API key for Freesound.org (optional)

### Cache Location

Sounds are cached in `~/.game-sfx-cache/` with the following structure:
```
~/.game-sfx-cache/
├── kenney/
├── freesound/
├── sonniss/
├── opengameart/
└── index.json
```

## Providers

### Kenney.nl
- **License**: CC0 (no attribution required)
- **Content**: UI sounds, game effects
- **Status**: ✅ Implemented

### Freesound.org
- **License**: Various (CC0, CC-BY)
- **Content**: Community-uploaded sounds
- **Status**: ✅ Implemented (requires API key)

### Sonniss GameAudioGDC
- **License**: Royalty-free
- **Content**: Professional game audio
- **Status**: 🚧 Placeholder (requires licensing)

### OpenGameArt.org
- **License**: Various (CC0, CC-BY)
- **Content**: Game-focused sound effects
- **Status**: ✅ Implemented

## Dependencies

- `@modelcontextprotocol/sdk`: MCP protocol implementation
- `fluent-ffmpeg`: Audio processing
- `node-fetch`: HTTP requests
- `zod`: Schema validation

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Troubleshooting

### FFmpeg Not Found
Install FFmpeg on your system:
- **macOS**: `brew install ffmpeg`
- **Ubuntu**: `sudo apt install ffmpeg`
- **Windows**: Download from https://ffmpeg.org/

### API Key Issues
Set your Freesound API key:
```bash
export FREESOUND_API_KEY="your_api_key_here"
```

### Permission Errors
Ensure the cache directory is writable:
```bash
chmod 755 ~/.game-sfx-cache
```
