{"name": "mcp-sfx-server", "version": "0.1.0", "type": "module", "description": "MCP server for boardgame SFX search, download, and normalization", "main": "index.js", "scripts": {"build": "tsc", "start": "node index.js", "dev": "tsx index.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "zod": "^3.22.0", "node-fetch": "^3.3.0", "fluent-ffmpeg": "^2.1.2", "fs-extra": "^11.2.0", "path": "^0.12.7", "os": "^0.1.2"}, "devDependencies": {"@types/node": "^20.0.0", "@types/fluent-ffmpeg": "^2.1.24", "@types/fs-extra": "^11.0.4", "typescript": "^5.0.0", "tsx": "^4.20.5"}}