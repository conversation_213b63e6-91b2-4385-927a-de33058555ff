// Test script to simulate a 3-player Scout game
const io = require('socket.io-client');

async function simulateGame() {
  console.log('🎮 Starting Scout Game Simulation - 3 Players');
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Player 1 creates room
    console.log('📝 Player 1 creating room...');
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    
    console.log(`✅ Room created: ${roomData.roomId}`);
    
    // Players 2 and 3 join room
    for (let i = 1; i < 3; i++) {
      console.log(`📝 Player ${i + 1} joining room...`);
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room: ${response?.error || 'Unknown error'}`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
      
      console.log(`✅ Player ${i + 1} joined room`);
    }
    
    // Wait a bit for room state to sync
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Start round
    console.log('🚀 Starting round with 3 players...');
    const startResult = await new Promise((resolve, reject) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, (success) => {
        resolve(success);
      });
    });
    
    if (startResult) {
      console.log('✅ Round started successfully!');
      
      // Set up event listeners to monitor game state
      players.forEach((player, index) => {
        player.socket.on('roundState', (roundState) => {
          console.log(`🎯 Player ${index + 1} received round state:`, {
            activePlayer: roundState.activePlayer,
            handSize: roundState.you?.hand?.length || 0,
            activeSet: roundState.activeSet?.cards?.length || 0,
            phase: roundState.phase,
            isMyTurn: roundState.activePlayer === players[index].playerId
          });
        });
        
        player.socket.on('roomState', (roomState) => {
          console.log(`🏠 Player ${index + 1} received room state:`, {
            currentRound: roomState.currentRound,
            totalRounds: roomState.totalRounds,
            playerCount: roomState.playerOrder.length
          });
        });
      });
      
      // Wait for game state to be distributed
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('🎮 Game simulation completed! Check the browser for the actual game interface.');
      
    } else {
      console.log('❌ Failed to start round');
    }
    
  } catch (error) {
    console.error('❌ Game simulation failed:', error);
  }
  
  // Keep connections alive for manual testing
  console.log('🔄 Keeping connections alive for manual testing...');
  console.log('💡 Open http://localhost:5173 in your browser to see the game interface');
}

simulateGame().catch(console.error);
