#!/usr/bin/env tsx
// Create sample SFX files for demonstration
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple WAV file generator for basic tones
function generateWAVFile(frequency: number, duration: number, sampleRate: number = 44100): Buffer {
  const numSamples = Math.floor(sampleRate * duration);
  const buffer = Buffer.alloc(44 + numSamples * 2); // WAV header + 16-bit samples
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // PCM format size
  buffer.writeUInt16LE(1, 20);  // PCM format
  buffer.writeUInt16LE(1, 22);  // Mono
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28); // Byte rate
  buffer.writeUInt16LE(2, 32);  // Block align
  buffer.writeUInt16LE(16, 34); // Bits per sample
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // Generate sine wave samples
  for (let i = 0; i < numSamples; i++) {
    const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate);
    const amplitude = Math.floor(sample * 16383); // 16-bit signed
    buffer.writeInt16LE(amplitude, 44 + i * 2);
  }
  
  return buffer;
}

// Generate envelope-shaped tone (attack, sustain, release)
function generateEnvelopedTone(frequency: number, duration: number, sampleRate: number = 44100): Buffer {
  const numSamples = Math.floor(sampleRate * duration);
  const buffer = Buffer.alloc(44 + numSamples * 2);
  
  // WAV header (same as above)
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(1, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28);
  buffer.writeUInt16LE(2, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // Generate enveloped sine wave
  const attackTime = duration * 0.1;
  const releaseTime = duration * 0.3;
  const sustainTime = duration - attackTime - releaseTime;
  
  for (let i = 0; i < numSamples; i++) {
    const t = i / sampleRate;
    let envelope = 1.0;
    
    if (t < attackTime) {
      envelope = t / attackTime; // Attack
    } else if (t > attackTime + sustainTime) {
      envelope = 1.0 - (t - attackTime - sustainTime) / releaseTime; // Release
    }
    
    envelope = Math.max(0, Math.min(1, envelope));
    
    const sample = Math.sin(2 * Math.PI * frequency * t) * envelope;
    const amplitude = Math.floor(sample * 16383);
    buffer.writeInt16LE(amplitude, 44 + i * 2);
  }
  
  return buffer;
}

// Generate noise burst (for error sounds)
function generateNoiseBurst(duration: number, sampleRate: number = 44100): Buffer {
  const numSamples = Math.floor(sampleRate * duration);
  const buffer = Buffer.alloc(44 + numSamples * 2);
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(1, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28);
  buffer.writeUInt16LE(2, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // Generate filtered noise with envelope
  for (let i = 0; i < numSamples; i++) {
    const t = i / sampleRate;
    const envelope = Math.exp(-t * 8); // Exponential decay
    const noise = (Math.random() - 0.5) * 2;
    const sample = noise * envelope * 0.3; // Lower volume for noise
    const amplitude = Math.floor(sample * 16383);
    buffer.writeInt16LE(amplitude, 44 + i * 2);
  }
  
  return buffer;
}

async function createSampleSounds(): Promise<void> {
  const outputDir = path.join(__dirname, '../apps/client/public/assets/sfx');
  
  // Ensure output directory exists
  await fs.mkdir(outputDir, { recursive: true });
  
  console.log('🎵 Creating sample SFX files...\n');
  
  const sounds = [
    {
      filename: 'ui_click_soft.wav',
      description: 'Soft UI click',
      generator: () => generateEnvelopedTone(800, 0.1)
    },
    {
      filename: 'ui_click_sharp.wav',
      description: 'Sharp UI click',
      generator: () => generateEnvelopedTone(1200, 0.08)
    },
    {
      filename: 'ui_confirmation.wav',
      description: 'UI confirmation chime',
      generator: () => {
        // Generate a pleasant chord (C major)
        const duration = 0.3;
        const sampleRate = 44100;
        const numSamples = Math.floor(sampleRate * duration);
        const buffer = Buffer.alloc(44 + numSamples * 2);
        
        // WAV header
        buffer.write('RIFF', 0);
        buffer.writeUInt32LE(36 + numSamples * 2, 4);
        buffer.write('WAVE', 8);
        buffer.write('fmt ', 12);
        buffer.writeUInt32LE(16, 16);
        buffer.writeUInt16LE(1, 20);
        buffer.writeUInt16LE(1, 22);
        buffer.writeUInt32LE(sampleRate, 24);
        buffer.writeUInt32LE(sampleRate * 2, 28);
        buffer.writeUInt16LE(2, 32);
        buffer.writeUInt16LE(16, 34);
        buffer.write('data', 36);
        buffer.writeUInt32LE(numSamples * 2, 40);
        
        // Generate C major chord (C, E, G)
        const frequencies = [523, 659, 784];
        for (let i = 0; i < numSamples; i++) {
          const t = i / sampleRate;
          const envelope = Math.exp(-t * 3);
          let sample = 0;
          
          frequencies.forEach(freq => {
            sample += Math.sin(2 * Math.PI * freq * t) * envelope / frequencies.length;
          });
          
          const amplitude = Math.floor(sample * 16383 * 0.7);
          buffer.writeInt16LE(amplitude, 44 + i * 2);
        }
        
        return buffer;
      }
    },
    {
      filename: 'ui_error.wav',
      description: 'UI error sound',
      generator: () => generateNoiseBurst(0.2)
    },
    {
      filename: 'ui_whoosh.wav',
      description: 'UI whoosh transition',
      generator: () => {
        // Generate a frequency sweep
        const duration = 0.4;
        const sampleRate = 44100;
        const numSamples = Math.floor(sampleRate * duration);
        const buffer = Buffer.alloc(44 + numSamples * 2);
        
        // WAV header
        buffer.write('RIFF', 0);
        buffer.writeUInt32LE(36 + numSamples * 2, 4);
        buffer.write('WAVE', 8);
        buffer.write('fmt ', 12);
        buffer.writeUInt32LE(16, 16);
        buffer.writeUInt16LE(1, 20);
        buffer.writeUInt16LE(1, 22);
        buffer.writeUInt32LE(sampleRate, 24);
        buffer.writeUInt32LE(sampleRate * 2, 28);
        buffer.writeUInt16LE(2, 32);
        buffer.writeUInt16LE(16, 34);
        buffer.write('data', 36);
        buffer.writeUInt32LE(numSamples * 2, 40);
        
        // Generate frequency sweep from 200Hz to 800Hz
        for (let i = 0; i < numSamples; i++) {
          const t = i / sampleRate;
          const progress = t / duration;
          const frequency = 200 + (800 - 200) * progress;
          const envelope = Math.sin(Math.PI * progress); // Bell curve envelope
          const sample = Math.sin(2 * Math.PI * frequency * t) * envelope;
          const amplitude = Math.floor(sample * 16383 * 0.5);
          buffer.writeInt16LE(amplitude, 44 + i * 2);
        }
        
        return buffer;
      }
    },
    {
      filename: 'card_flip.wav',
      description: 'Card flip sound',
      generator: () => generateEnvelopedTone(400, 0.15)
    },
    {
      filename: 'card_slide.wav',
      description: 'Card slide sound',
      generator: () => generateEnvelopedTone(300, 0.2)
    },
    {
      filename: 'card_shuffle.wav',
      description: 'Card shuffle sound',
      generator: () => generateNoiseBurst(0.5)
    },
    {
      filename: 'wood_tap_light.wav',
      description: 'Light wood tap',
      generator: () => generateEnvelopedTone(150, 0.1)
    },
    {
      filename: 'success_chime.wav',
      description: 'Success chime',
      generator: () => generateEnvelopedTone(880, 0.25)
    },
    {
      filename: 'victory_fanfare.wav',
      description: 'Victory fanfare',
      generator: () => {
        // Generate a victory melody
        const notes = [523, 659, 784, 1047]; // C, E, G, C
        const noteDuration = 0.2;
        const totalDuration = notes.length * noteDuration;
        const sampleRate = 44100;
        const numSamples = Math.floor(sampleRate * totalDuration);
        const buffer = Buffer.alloc(44 + numSamples * 2);
        
        // WAV header
        buffer.write('RIFF', 0);
        buffer.writeUInt32LE(36 + numSamples * 2, 4);
        buffer.write('WAVE', 8);
        buffer.write('fmt ', 12);
        buffer.writeUInt32LE(16, 16);
        buffer.writeUInt16LE(1, 20);
        buffer.writeUInt16LE(1, 22);
        buffer.writeUInt32LE(sampleRate, 24);
        buffer.writeUInt32LE(sampleRate * 2, 28);
        buffer.writeUInt16LE(2, 32);
        buffer.writeUInt16LE(16, 34);
        buffer.write('data', 36);
        buffer.writeUInt32LE(numSamples * 2, 40);
        
        // Generate melody
        for (let i = 0; i < numSamples; i++) {
          const t = i / sampleRate;
          const noteIndex = Math.floor(t / noteDuration);
          const noteTime = t - noteIndex * noteDuration;
          
          if (noteIndex < notes.length) {
            const frequency = notes[noteIndex];
            const envelope = Math.exp(-noteTime * 4);
            const sample = Math.sin(2 * Math.PI * frequency * noteTime) * envelope;
            const amplitude = Math.floor(sample * 16383 * 0.8);
            buffer.writeInt16LE(amplitude, 44 + i * 2);
          } else {
            buffer.writeInt16LE(0, 44 + i * 2);
          }
        }
        
        return buffer;
      }
    },
    {
      filename: 'round_transition.wav',
      description: 'Round transition',
      generator: () => generateEnvelopedTone(440, 0.3)
    }
  ];
  
  for (const sound of sounds) {
    try {
      console.log(`Creating ${sound.filename} - ${sound.description}`);
      const audioData = sound.generator();
      const filePath = path.join(outputDir, sound.filename);
      await fs.writeFile(filePath, audioData);
      console.log(`✅ Created: ${filePath}`);
    } catch (error) {
      console.error(`❌ Failed to create ${sound.filename}:`, error);
    }
  }
  
  console.log('\n🎵 Sample SFX creation completed!');
  console.log(`📁 Files saved to: ${outputDir}`);
  console.log('\n📋 Next steps:');
  console.log('1. Start the Scout client: npm run dev (in apps/client)');
  console.log('2. Open settings panel and enable "Use Production Sounds"');
  console.log('3. Test the sounds using the test buttons');
  console.log('4. Play the game and enjoy the enhanced audio experience!');
}

// Run the script
createSampleSounds().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
