# Assets Plan

Phase 1 (procedural):
- Render cards as SVG procedurally in the client. See `apps/client/src/ui/Card.tsx`.
- Define a `CardSkin` interface to allow swapping themes later.
- Tokens (Scout, Scout&Show, score) will be simple SVGs first.

Phase 2 (sliced skins):
- Slice `RnD/cards.png` into individual images (one‑time script) and export to `public/cards/classic/{i}-{j}.png`.
- Convert `RnD/cards_alt_design*.pdf` into SVG/PNG per card (e.g., via `pdftocairo`), saved under `public/cards/alt/`.
- Card component will support `skin: 'procedural' | 'classic' | 'alt'`.

Licensing: Per user note, no constraints for internal use here.

