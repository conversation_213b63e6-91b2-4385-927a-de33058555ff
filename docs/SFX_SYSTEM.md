# Scout SFX System

This document describes the production-grade sound effects system implemented for the Scout card game.

## Overview

The Scout SFX system replaces procedural sound generation with high-quality, licensed sound effects from multiple sources. It includes:

- **MCP SFX Server**: A Model Context Protocol server for searching, downloading, and normalizing audio
- **Production Sound Assets**: Curated SFX from Kenney, Freesound, Sonniss, and OpenGameArt
- **Enhanced Sound Service**: Upgraded client-side audio system with caching and fallbacks
- **Sound Manifest**: Centralized configuration mapping game events to audio assets

## Architecture

### MCP SFX Server (`exp/mcp-sfx-server/`)

The MCP server provides tools for:
- `search_sfx`: Search across multiple SFX providers
- `download_sfx`: Download and normalize specific sounds
- `normalize_sfx`: Process existing audio files
- `catalog_sfx`: Get recommended sound categories

**Providers:**
- **Kenney.nl**: CC0 UI sounds (no attribution required)
- **Freesound.org**: Community sounds with API integration
- **Sonniss GameAudioGDC**: Professional game audio (royalty-free)
- **OpenGameArt.org**: CC0 game assets

### Audio Normalization

All sounds are processed to game-ready specifications:
- **Sample Rate**: 48kHz
- **Peak Level**: -3dBFS
- **Channels**: Mono for UI, stereo for ambient
- **Formats**: OGG (web) and WAV (source)

### Sound Manifest (`apps/client/src/services/soundManifest.ts`)

Centralized configuration defining:
- **Assets**: Sound file metadata with licensing info
- **Events**: Mapping from game events to sound assets
- **Categories**: Organization by sound type (ui, card, token, game, ambient)

### Enhanced Sound Service (`apps/client/src/services/soundService.ts`)

Upgraded client-side audio system featuring:
- **Production SFX**: High-quality audio files with fallback to procedural
- **Audio Caching**: Preloading and memory management
- **Volume Control**: Per-asset and global volume settings
- **License Tracking**: Audit trail for all audio assets

## Game Event Mapping

| Game Event | Sound Asset | Description |
|------------|-------------|-------------|
| `card.select` | `ui_click_soft` | Card selection |
| `card.deselect` | `ui_click_soft` | Card deselection |
| `card.flip` | `card_flip` | Card orientation change |
| `action.show` | `ui_confirmation` | Show cards action |
| `action.scout` | `ui_whoosh` | Scout action |
| `action.pass` | `ui_click_sharp` | Pass turn |
| `game.error` | `ui_error` | Invalid action |
| `game.victory` | `victory_fanfare` | Game win |
| `round.end` | `round_transition` | Round completion |

## Usage

### Development Setup

1. **Install Dependencies**:
   ```bash
   cd exp/mcp-sfx-server
   npm install
   ```

2. **Configure API Keys** (optional):
   ```bash
   export FREESOUND_API_KEY="your_freesound_api_key"
   ```

3. **Test the System**:
   ```bash
   cd scripts
   npm install
   npx tsx test-sfx-system.ts
   ```

### Download Production Sounds

```bash
cd scripts
npm run download-sfx
```

This downloads and normalizes sound files to `apps/client/public/assets/sfx/`.

### MCP Server Integration

The MCP server can be integrated with Augment or other MCP clients:

```bash
# Run the server
cd exp/mcp-sfx-server
npm run dev

# Or build and run
npm run build
npm start
```

### Client-Side Usage

The sound service automatically uses production sounds when available:

```typescript
import { soundService } from '../services/soundService';

// Play production sound with procedural fallback
soundService.cardSelect();

// Configure sound system
soundService.setUseProductionSounds(true);
soundService.preloadSounds();

// Get cache statistics
const stats = soundService.getCacheStats();
```

## Settings Panel

The enhanced settings panel includes:
- **Sound Toggle**: Enable/disable all sounds
- **Volume Slider**: Global volume control
- **Production Sounds Toggle**: Switch between production and procedural
- **Preload Button**: Cache all sounds for better performance
- **Clear Cache**: Free memory and reload sounds
- **Statistics**: Display cache status and manifest info

## File Structure

```
Scout/
├── exp/mcp-sfx-server/           # MCP server implementation
│   ├── index.ts                  # Main server entry point
│   ├── lib/
│   │   ├── sfx-manager.ts        # Core SFX management
│   │   ├── providers/            # SFX provider integrations
│   │   ├── audio/                # Audio processing utilities
│   │   └── cache/                # Caching and licensing
│   └── package.json
├── apps/client/
│   ├── src/services/
│   │   ├── soundService.ts       # Enhanced sound service
│   │   └── soundManifest.ts      # Sound asset configuration
│   └── public/assets/sfx/        # Production sound files
├── scripts/
│   ├── download-sfx.ts           # SFX download automation
│   └── test-sfx-system.ts        # System testing
└── docs/
    └── SFX_SYSTEM.md             # This documentation
```

## Licensing

All sound assets are properly licensed:
- **CC0**: No attribution required (Kenney, some OpenGameArt)
- **CC-BY**: Attribution required (some Freesound, OpenGameArt)
- **ROYALTY_FREE**: Commercial use allowed (Sonniss)

License information is tracked in the sound manifest and cache system for audit compliance.

## Performance

- **Lazy Loading**: Sounds loaded on first use
- **Preloading**: Optional bulk loading for better UX
- **Caching**: Memory-based cache with cleanup
- **Fallbacks**: Graceful degradation to procedural sounds
- **Format Optimization**: OGG for web, optimized file sizes

## Future Enhancements

- **Dynamic Loading**: Load sounds based on game state
- **Spatial Audio**: 3D positioning for immersive experience
- **Adaptive Quality**: Adjust quality based on connection
- **User Uploads**: Allow custom sound packs
- **Analytics**: Track sound usage and performance
