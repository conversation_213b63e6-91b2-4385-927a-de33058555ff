# Scout Web App – Context & Decisions

This document captures the conversation to date, goals, rules synthesis, open questions, and the technical plan so that engineers and AI agents have complete context while working in this repository.

## Conversation Summary

- Goal: Implement the board game “SCOUT” (2019, Oink Games) as a React + Node + WebSockets web application.
- Assets: Rules text and PDFs live in `RnD/`. Card collage is `RnD/cards.png` and `RnD/cards.webp`. Alternate card designs are `RnD/cards_alt_design.pdf` and `RnD/cards_alt_design2.pdf`.
- Requirements:
  - Include the 2-player variant from the outset.
  - UI/UX must be extremely polished and professional. Use Tailwind, and any additional technologies required to deliver a studio‑grade result.
  - No licensing concerns for the provided assets.
  - Procedural card rendering in Phase 1; sliced/alternate assets can be wired later.
  - Turn‑order nuance when the last card of the active set is scouted away must be documented as a TODO clarification.
  - Document the entire context, rules and plan for handoff.

## Rules Synthesis (based on `RnD/rules1.txt` and deck analysis)

- Players: 3–5 for standard rules. A 2‑player variant exists (see below).
- Deck: 45 cards. Each card shows two different numbers from 1–10; the canonical deck is every unordered pair (i, j) where 10 ≥ i > j ≥ 1 (45 = C(10, 2)).
- Setup by player count:
  - 3p: Remove all cards that include a 10. Deal 12 cards each.
  - 4p: Remove only the 10|9 card. Deal 11 cards each.
  - 5p: Use all cards. Deal 9 cards each.
  - 2p: Implemented as a separate variant. See “Two‑Player Variant” below.
- Hand handling:
  - Before the round begins, each player may flip their entire hand to use the opposite numbers. After that, individual card orientation and order are fixed; you may not reorder cards in hand.
- Turn actions:
  - Show: Play a contiguous block from your hand as either a Kind (all equal values) or a Run (consecutive ascending or descending). The set must beat the current active set per hierarchy: longer length > shorter; Kind beats Run of equal length; if same type and length, the higher minimum value wins; you may not play an identical set.
    - If you beat it, replace the active set and take the previous set facedown as points.
  - Scout: Take one edge card (leftmost or rightmost) from the active set; insert it anywhere in your hand in any orientation. The owner of that active set gains one Scout token. The active set remains on the table with the card removed. If the set becomes empty, see the TODO question below.
  - Scout & Show: Once per round per player, immediately after scouting, you may also show a set in the same turn; spend your Scout&Show token.
- Round end:
  - The round ends immediately when any player empties their hand, OR when after a set is shown, every other player fails to beat it during their next turns.
- Scoring:
  - +1 per facedown card collected.
  - +1 per Scout token held.
  - −1 per card remaining in hand, except the player whose set ended the round by remaining unbeaten (they suffer no negatives for cards in hand).
  - Play as many rounds as players; highest total score wins. Ties shared.

### Two‑Player Variant (Official Oink Games Rules - FINALIZED)

The server/engine now implements the official 2‑player variant under `variant: 'two-player'` with the following confirmed rules:

- **Deck Setup**: Remove the single 10|9 card from the deck (same as 4-player setup)
- **Deal**: 11 cards to each player
- **Rounds**: Play exactly 2 rounds (instead of the standard "as many rounds as players")
- **Scout Tokens**: Standard Scout token rules apply
- **All other gameplay**: Follows standard rules for showing, scouting, passing, and scoring

## Open Questions (Clarifications Needed)

1) When the last remaining card of the active set is scouted away, who takes the next turn?
   - Options we’ve seen in table rules:
     a) The owner of the now‑empty set immediately takes the next turn (since their set is gone).
     b) Turn order simply proceeds to the next player after the scouting player (i.e., no interruption).
   - Please confirm the official rule, including whether the “unbeaten since” tracker resets in this situation.

2) ~~Two‑Player Variant specifics~~ **RESOLVED**: Official 2-player rules have been implemented:
   - ✅ Remove the single 10|9 card from the deck
   - ✅ Deal 11 cards to each player
   - ✅ Play exactly 2 rounds
   - ✅ Standard Scout token rules apply
   - ✅ All other rules follow standard gameplay

## Technical Plan (High‑Level)

- Monorepo (npm workspaces):
  - `apps/server`: Node + Express + Socket.IO authoritative server.
  - `apps/client`: React + Vite + Tailwind professional UI.
  - `packages/game`: Pure TypeScript engine (rules/validators/scoring/deck).
  - `packages/protocol`: Shared types and socket contracts.
  - `docs/`: Rules and context.
- Assets: Phase 1 uses procedural SVG card rendering; Phase 2 can add sliced classic/alt skins from `RnD/`.
- Networking: Versioned, authoritative updates from the server; client sends intents (indices, edge, etc.).
- Security: Server validates all moves; no client reordering; hidden information respected.
- Testing: Unit tests in `packages/game` for set detection and comparisons; e2e scripted after basic loop lands.

## Current Status (initial commit)

- Monorepo scaffolded with server, client, protocol, and game engine packages.
- Engine implements canonical deck and the core “show” and “scout” transitions; includes provisional 2p setup.
- Server exposes minimal Socket.IO room + round lifecycle; authoritative state is held server‑side.
- Client has a basic lobby UI (Tailwind) and can create/join rooms and start rounds. Table UI and full gameplay UI are next.

## Next Steps

1. Confirm the two‑player variant details and the turn‑order nuance; update the engine and docs.
2. Implement the full Table UI:
   - Hand strip with contiguous selection and orientation preview.
   - Active set view with pickable edges for scouting.
   - Token indicators and once‑per‑round Scout&Show affordance.
   - Mobile‑first responsive layout and keyboard shortcuts.
3. Scoring view and multi‑round flow.
4. Polished procedural card renderer with themes and motion; then optional asset skins from `RnD/`.
5. Unit tests and e2e.

