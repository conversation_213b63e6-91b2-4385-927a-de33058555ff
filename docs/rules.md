# SCOUT — Rules (Consolidated)

This is a consolidated rules summary for SCOUT, based on the materials found in `RnD/` and the canonical deck analysis. It is intended as the authoritative reference for this web implementation.

## Components
- 45 double‑number cards, covering every unordered pair of values 1–10 (i, j) where i > j.
- 23 Scout tokens, 30 Score tokens, 5 Scout&Show tokens, starting player marker.

## Player Counts
- Standard: 3–5 players
- Variant: 2 players (official Oink Games variant)

## Setup
- Choose a starting player; give each player a Scout&Show token.
- Prepare the deck by player count:
  - 3 players: Remove all cards containing a 10.
  - 4 players: Remove the single 10|9 card.
  - 5 players: Use all 45 cards.
  - 2 players: Remove the single 10|9 card (same as 4-player setup).
- Shuffle and deal:
  - 3 players: 12 cards each
  - 4 players: 11 cards each
  - 5 players: 9 cards each
  - 2 players: 11 cards each
- Players keep hands hidden and may not reorder or independently rotate cards at any time.
- Before the first turn, each player may flip their entire hand to use the opposite numbers on all cards.

## Gameplay
On your turn, choose one of the following actions:

1) Show
- Play a contiguous slice of cards from your hand as either a Kind (all equal values) or a Run (consecutive, either strictly ascending or strictly descending).
- The set must beat the current active set on the table following this hierarchy:
  - Longer set > shorter set.
  - Kind > Run of the same length.
  - If same type and length, the set with the higher minimum value wins.
  - If identical in type, length, and minimum value, you may not play it.
- If you beat the active set, replace it; take the previous active set as facedown cards for scoring.

2) Scout
- Take one edge card (leftmost or rightmost) from the current active set.
- Insert it anywhere in your hand in any orientation (choose which number is active for that card).
- The owner of the active set receives one Scout token.
- The active set remains on the table with that card removed. If it becomes empty, see the open question below.

3) Scout & Show (once per round)
- After scouting, you may immediately perform a Show. Spend your Scout&Show token to do so (one time each round).

## Round End
- The round ends immediately when either:
  - A player empties their hand; or
  - After a player shows a set, every other player takes their next turn and fails to beat it (they either scout or cannot beat it).

## Scoring
- +1 point for each facedown card collected.
- +1 point for each Scout token held.
- −1 point for each card remaining in your hand, except for the player whose last show ended the round by remaining unbeaten (they take no negative points for cards in hand).
- Play as many rounds as there are players (except 2-player: only 2 rounds); most points wins. Ties are shared.

## Two‑Player Variant (Official Oink Games Rules)
- **Setup**: Remove the single 10|9 card from the deck (same as 4-player setup).
- **Deal**: 11 cards to each player.
- **Rounds**: Play exactly 2 rounds (instead of the standard "as many rounds as players").
- **Scout Tokens**: Standard Scout token rules apply - players receive Scout tokens when their active set is scouted from.
- **All other rules**: Follow standard gameplay, scoring, and round-end conditions.

## Open Questions (Clarifications)
- Turn order after the last card of the active set is scouted away: who plays next, and does the “unbeaten since” status reset? Please confirm the official rule.
- Two‑player specifics detailed above.

## Notes on Accuracy
- The deck composition used by this implementation strictly matches every unordered pair of values from 1 to 10 (no duplicates, no same‑number pairs). This corresponds to the physical set visible in `RnD/cards.png`.

