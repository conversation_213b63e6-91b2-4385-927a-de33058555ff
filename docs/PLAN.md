# Implementation Plan (Updated)

This document tracks the detailed plan to deliver a professional web implementation of SCOUT.

## Goals
- Studio‑grade UI/UX (Tailwind + custom visuals), responsive and mobile‑first.
- Accurate rules for 3–5 players and the official 2‑player variant.
- Procedural card rendering in Phase 1; skin system for sliced image assets in Phase 2.
- Reliable networking with reconnection and per‑player hidden information.

## Milestones
1) Core Engine & Protocol
   - Deck generation and player‑count filtering (done)
   - Set detection, comparisons, and move validation (in progress)
   - Round end and scoring (pending)
   - 2p variant finalized (pending clarification)

2) Server
   - Room creation/join; authoritative engine (initial skeleton in place)
   - Per‑player state emission (pending refinement)
   - Round lifecycle + scoring (pending)

3) Client
   - Lobby flows (initial skeleton in place)
   - Table UI: hand selection, show/scout actions, tokens, active set panel (pending)
   - Procedural card renderer and effects (pending)
   - Scoreboard + multi‑round flow (pending)

4) Polish & QA
   - World‑class art direction pass (motion, sound, haptics)
   - Accessibility and keyboard shortcuts
   - Unit tests (engine) + e2e (game loop)

## Design System & Visual Direction
- Tailwind with a custom theme (`brand` palette) and CSS variables.
- Components: AppS<PERSON>, Card (procedural SVG), Token, PlayerBadge, SetRow, ControlsBar, ScoreSheet.
- Motion: smooth sequencing for Show/Scout, hover/press affordances, confetti on end‑round wins.
- Mobile: thumb‑oriented controls, horizontal hand scroll, large tap targets, edge selectors.

## Notes
- All move requests contain only indices and intent; server authoritatively updates state.
- The engine exposes a deterministic seeded shuffle for reproducibility.
- The codebase is structured as npm workspaces; run `npm run dev` at repo root for both client/server.

