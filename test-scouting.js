// Test script to simulate Scout scouting mechanics
const io = require('socket.io-client');

async function testScouting() {
  console.log('🎮 Testing Scout Scouting Mechanics - 3 Players');
  
  // Create 3 players
  const players = [];
  for (let i = 0; i < 3; i++) {
    const socket = io('http://localhost:4000', { 
      autoConnect: true, 
      transports: ['websocket'] 
    });
    
    players.push({
      id: i + 1,
      name: `Player${i + 1}`,
      socket,
      roomId: null,
      playerId: null,
      token: null,
      hand: [],
      isMyTurn: false,
      activeSet: null
    });
  }
  
  // Wait for connections
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Set up game (same as before)
    console.log('📝 Setting up 3-player game...');
    
    const roomData = await new Promise((resolve, reject) => {
      players[0].socket.emit('createRoom', { name: players[0].name }, (response) => {
        if (response && response.roomId) {
          resolve(response);
        } else {
          reject(new Error('Failed to create room'));
        }
      });
    });
    
    players[0].roomId = roomData.roomId;
    players[0].playerId = roomData.playerId;
    players[0].token = roomData.token;
    
    for (let i = 1; i < 3; i++) {
      const joinData = await new Promise((resolve, reject) => {
        players[i].socket.emit('joinRoom', { 
          roomId: roomData.roomId, 
          name: players[i].name 
        }, (response) => {
          if (response && response.ok) {
            resolve(response);
          } else {
            reject(new Error(`Failed to join room`));
          }
        });
      });
      
      players[i].roomId = roomData.roomId;
      players[i].playerId = joinData.playerId;
      players[i].token = joinData.token;
    }
    
    const startResult = await new Promise((resolve) => {
      players[0].socket.emit('startRound', { 
        playerCount: 3, 
        variant: 'standard' 
      }, (success) => {
        resolve(success);
      });
    });
    
    if (!startResult) {
      throw new Error('Failed to start round');
    }
    
    console.log('✅ Game setup complete!');
    
    // Set up event listeners
    players.forEach((player, index) => {
      player.socket.on('roundState', (roundState) => {
        player.hand = roundState.you?.hand || [];
        player.isMyTurn = roundState.activePlayer === player.playerId;
        player.activeSet = roundState.activeSet;
        
        console.log(`🎯 Player ${index + 1} state:`, {
          handSize: player.hand.length,
          isMyTurn: player.isMyTurn,
          activeSetSize: player.activeSet?.cards?.length || 0,
          phase: roundState.phase
        });
      });
    });
    
    // Wait for initial state
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 1: Player 1 shows a card to create active set
    console.log('\n🧪 TEST 1: Create Active Set');
    let currentPlayer = players.find(p => p.isMyTurn);
    if (currentPlayer) {
      // Choose initial orientation
      await new Promise((resolve) => {
        currentPlayer.socket.emit('chooseInitialHandOrientation', { orientation: 'a' }, resolve);
      });
      
      console.log(`Player ${currentPlayer.id} showing first card...`);
      const showResult = await new Promise((resolve) => {
        currentPlayer.socket.emit('show', { 
          start: 0, 
          endInclusive: 0 
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Show result: ${showResult.ok ? '✅ Success' : `❌ Failed: ${showResult.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 2: Next player scouts from the active set
    console.log('\n🧪 TEST 2: Scout from Active Set');
    currentPlayer = players.find(p => p.isMyTurn);
    if (currentPlayer && currentPlayer.activeSet && currentPlayer.activeSet.cards.length > 0) {
      console.log(`Player ${currentPlayer.id} attempting to scout from active set...`);
      console.log(`Active set: ${currentPlayer.activeSet.cards.map(c => c.cardId).join(', ')}`);
      
      const scoutResult = await new Promise((resolve) => {
        currentPlayer.socket.emit('scout', { 
          edge: 'left',  // Scout from left edge
          insertAt: 0,   // Insert at beginning of hand
          orientation: 'a'
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Scout result: ${scoutResult.ok ? '✅ Success' : `❌ Failed: ${scoutResult.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test 3: Check if scouting worked correctly
    console.log('\n🧪 TEST 3: Verify Scouting Results');
    const scoutingPlayer = players.find(p => p.isMyTurn);
    if (scoutingPlayer) {
      console.log(`Player ${scoutingPlayer.id} hand after scouting:`, {
        handSize: scoutingPlayer.hand.length,
        firstCard: scoutingPlayer.hand[0]?.cardId,
        activeSetSize: scoutingPlayer.activeSet?.cards?.length || 0
      });
    }
    
    // Test 4: Try Scout & Show combo
    console.log('\n🧪 TEST 4: Scout & Show Combo');
    if (scoutingPlayer && scoutingPlayer.activeSet && scoutingPlayer.activeSet.cards.length > 0) {
      console.log(`Player ${scoutingPlayer.id} attempting Scout & Show...`);
      
      const scoutShowResult = await new Promise((resolve) => {
        scoutingPlayer.socket.emit('scout', { 
          edge: 'right',
          insertAt: scoutingPlayer.hand.length,
          orientation: 'a',
          useScoutAndShow: true
        }, (ok, error) => {
          resolve({ ok, error });
        });
      });
      console.log(`Scout & Show result: ${scoutShowResult.ok ? '✅ Success' : `❌ Failed: ${scoutShowResult.error}`}`);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n🎮 Scouting test completed!');
    console.log('💡 Open http://localhost:5173 in your browser to continue playing manually');
    
  } catch (error) {
    console.error('❌ Scouting test failed:', error);
  }
  
  // Keep connections alive
  console.log('\n🔄 Keeping connections alive for manual testing...');
}

testScouting().catch(console.error);
